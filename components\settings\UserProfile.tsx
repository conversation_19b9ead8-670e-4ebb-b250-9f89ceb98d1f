import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { useNotifications } from '../../hooks/useNotifications';
import { errorHandlingService } from '../../services/errorHandlingService';
import { notificationService } from '../../services/notificationService';
import { PasswordValidationResult } from '../../utils/passwordValidation';
import { useResponsive } from '../../utils/responsive';
import { PasswordInput } from '../common/PasswordInput';

const { width } = Dimensions.get('window');

export const UserProfile: React.FC = () => {
  const { user, updateProfile, changePassword } = useAuth();
  const notifications = useNotifications();
  const { getResponsiveStyles, fontSize, spacing } = useResponsive();
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const [editForm, setEditForm] = useState({
    name: user?.name || '',
    email: user?.email || '',
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Password validation states
  const [newPasswordValidation, setNewPasswordValidation] = useState<PasswordValidationResult | null>(null);
  const [confirmPasswordError, setConfirmPasswordError] = useState<string>('');

  // Password validation handlers
  const handleNewPasswordValidation = (result: PasswordValidationResult) => {
    setNewPasswordValidation(result);
  };

  const validateConfirmPassword = (confirmPassword: string) => {
    if (confirmPassword !== passwordForm.newPassword) {
      setConfirmPasswordError('🔄 Passwords match nahi kar rahe! Same password daal! 🤝');
    } else {
      setConfirmPasswordError('');
    }
  };

  // Update form when user data changes
  useEffect(() => {
    if (user) {
      setEditForm({
        name: user.name || '',
        email: user.email || '',
      });
    }
  }, [user]);

  const handleSaveProfile = async () => {
    // Add null checks to prevent trim errors
    const name = editForm.name || '';
    const email = editForm.email || '';

    if (!name.trim()) {
      Alert.alert('Error', 'Name cannot be empty');
      return;
    }

    if (!email.trim() || !isValidEmail(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    try {
      setIsLoading(true);

      // Show loading notification
      const loadingToast = notificationService.loading('🔄 Profile update ho raha hai... Wait kar thoda! ⏳');

      // Use regular API call with enhanced error handling
      await updateProfile({
        name: name.trim(),
        email: email.trim(),
      });

      // Dismiss loading and show success
      if (loadingToast) notificationService.dismiss(loadingToast);
      notificationService.profileUpdateSuccess();
      setIsEditing(false);

    } catch (error: any) {
      // Enhanced error handling for token expiration
      if (error?.code === 'TOKEN_EXPIRED') {
        // Token expired - show friendly message
        notificationService.error(
          '⏰ Session expire ho gaya! Dobara login kar bhai! 🔄',
          'Your session has expired. Please login again.'
        );
        // User will be automatically redirected to login by AuthContext
      } else {
        // Other errors
        errorHandlingService.handleError(error, 'Profile Update');
      }
    } finally {
      setIsLoading(false);
    }

  };

  const handleChangePassword = async () => {
    // Comprehensive validation
    if (!passwordForm.currentPassword) {
      Alert.alert('Current Password Required', '🔑 Current password toh daal! Verify karna hai! 🔐');
      return;
    }

    if (!passwordForm.newPassword) {
      Alert.alert('New Password Required', '🔒 New password daal bhai! 🆕');
      return;
    }

    if (!passwordForm.confirmPassword) {
      Alert.alert('Confirm Password Required', '🔄 Confirm password bhi daal! 🤝');
      return;
    }

    // Check new password validation
    if (!newPasswordValidation || !newPasswordValidation.isValid) {
      Alert.alert('Password Requirements', '🔒 Password requirements puri nahi hui! Check kar le! ✅');
      return;
    }

    // Check password confirmation
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      Alert.alert('Password Mismatch', '🔄 Passwords match nahi kar rahe! Same password daal! 🤝');
      return;
    }

    try {
      setIsLoading(true);

      // Show loading notification
      const loadingToast = notificationService.loading('🔐 Password change ho raha hai... Security tight kar rahe hain! 🛡️');

      await changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });

      // Dismiss loading and show success
      if (loadingToast) notificationService.dismiss(loadingToast);
      notificationService.success('🎉 Password change ho gaya! Ab tu secure hai! 🔒✨');

      setIsChangingPassword(false);
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (error: any) {
      // Enhanced error handling for token expiration
      if (error?.code === 'TOKEN_EXPIRED') {
        // Token expired - show friendly message
        notificationService.error(
          '⏰ Session expire ho gaya! Dobara login kar bhai! 🔄',
          'Your session has expired. Please login again.'
        );
        // User will be automatically redirected to login by AuthContext
      } else {
        // Other errors
        errorHandlingService.handleError(error, 'Password Change');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const cancelEdit = () => {
    setEditForm({
      name: user?.name || '',
      email: user?.email || '',
    });
    setIsEditing(false);
  };

  const cancelPasswordChange = () => {
    setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
    setIsChangingPassword(false);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Ionicons name="person-circle" size={60} color="#fff" />
          <Text style={styles.headerTitle}>User Profile</Text>
          <Text style={styles.headerSubtitle}>Manage your account information</Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        {/* Profile Information Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Profile Information</Text>
            {!isEditing && (
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => setIsEditing(true)}
              >
                <Ionicons name="pencil" size={20} color="#667eea" />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            )}
          </View>

          {isEditing ? (
            <View style={styles.editForm}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Name</Text>
                <TextInput
                  style={styles.input}
                  value={editForm.name}
                  onChangeText={(text) => setEditForm(prev => ({ ...prev, name: text }))}
                  placeholder="Enter your name"
                  editable={!isLoading}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Email</Text>
                <TextInput
                  style={styles.input}
                  value={editForm.email}
                  onChangeText={(text) => setEditForm(prev => ({ ...prev, email: text }))}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  editable={!isLoading}
                />
              </View>

              <View style={styles.buttonRow}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={cancelEdit}
                  disabled={isLoading}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.saveButton]}
                  onPress={handleSaveProfile}
                  disabled={isLoading}
                >
                  <Text style={styles.saveButtonText}>
                    {isLoading ? 'Saving...' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.profileInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Name:</Text>
                <Text style={styles.infoValue}>{user?.name}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Email:</Text>
                <Text style={styles.infoValue}>{user?.email}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Gender:</Text>
                <Text style={styles.infoValue}>
                  {user?.gender === 'male' ? '👨 Male' : '👩 Female'}
                </Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Member Since:</Text>
                <Text style={styles.infoValue}>
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Password Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Password</Text>
            {!isChangingPassword && (
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => setIsChangingPassword(true)}
              >
                <Ionicons name="key" size={20} color="#667eea" />
                <Text style={styles.editButtonText}>Change</Text>
              </TouchableOpacity>
            )}
          </View>

          {isChangingPassword ? (
            <View style={styles.editForm}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Current Password</Text>
                <TextInput
                  style={styles.input}
                  value={passwordForm.currentPassword}
                  onChangeText={(text) => setPasswordForm(prev => ({ ...prev, currentPassword: text }))}
                  placeholder="Enter current password"
                  secureTextEntry
                  editable={!isLoading}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>New Password</Text>
                <PasswordInput
                  value={passwordForm.newPassword}
                  onChangeText={(text) => setPasswordForm(prev => ({ ...prev, newPassword: text }))}
                  placeholder="Create a strong new password"
                  validationType="change"
                  showStrengthIndicator={true}
                  showValidationErrors={true}
                  onValidationChange={handleNewPasswordValidation}
                  style={styles.passwordInputField}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Confirm New Password</Text>
                <PasswordInput
                  value={passwordForm.confirmPassword}
                  onChangeText={(text) => {
                    setPasswordForm(prev => ({ ...prev, confirmPassword: text }));
                    validateConfirmPassword(text);
                  }}
                  placeholder="Confirm your new password"
                  validationType="login" // No validation rules, just basic input
                  showStrengthIndicator={false}
                  showValidationErrors={false}
                  style={styles.passwordInputField}
                />
                {confirmPasswordError ? (
                  <Text style={styles.confirmPasswordError}>{confirmPasswordError}</Text>
                ) : null}
              </View>

              <View style={styles.buttonRow}>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={cancelPasswordChange}
                  disabled={isLoading}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, styles.saveButton]}
                  onPress={handleChangePassword}
                  disabled={isLoading}
                >
                  <Text style={styles.saveButtonText}>
                    {isLoading ? 'Changing...' : 'Change Password'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.passwordInfo}>
              <Text style={styles.passwordText}>••••••••</Text>
              <Text style={styles.passwordSubtext}>
                Last changed: Not available
              </Text>
            </View>
          )}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    paddingBottom: 100,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 10,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginTop: 5,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 25,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2c3e50',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#667eea',
  },
  editButtonText: {
    fontSize: 14,
    color: '#667eea',
    marginLeft: 5,
    fontWeight: '600',
  },
  profileInfo: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f2f6',
  },
  infoLabel: {
    fontSize: 15,
    color: '#7f8c8d',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 15,
    color: '#2c3e50',
    fontWeight: '600',
  },
  editForm: {
    gap: 15,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#7f8c8d',
    fontWeight: '600',
  },
  saveButton: {
    backgroundColor: '#667eea',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  passwordInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  passwordText: {
    fontSize: 24,
    color: '#2c3e50',
    letterSpacing: 4,
    marginBottom: 8,
  },
  passwordSubtext: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  passwordInputField: {
    marginBottom: 0,
  },
  confirmPasswordError: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});
