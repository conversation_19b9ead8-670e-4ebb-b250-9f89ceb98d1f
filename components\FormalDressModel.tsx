import { useGLTF } from '@react-three/drei';
import { Asset } from 'expo-asset';
import React, { useEffect, useRef, useState } from 'react';
import { Color, FrontSide, LinearFilter, MeshStandardMaterial, RepeatWrapping } from 'three';
import { ColorCombination } from '../services/colorService';

// Completely independent FormalDressModel - totally different from FemaleModel
interface FormalDressModelProps {
    dressColors?: ColorCombination;
    modelScale?: number;
    verticalOffset?: number;
    enableColorSync?: boolean;
    displayMode?: 'front' | 'back';
    rotationY?: number; // Y-axis rotation (left/right) - full 360 degrees
    tiltX?: number; // X-axis tilt (forward/back)
}

// Completely independent FormalDressModel - unique implementation
export function FormalDressModel({
    dressColors,
    modelScale = 0.01, // Much more zoomed out
    verticalOffset = 0,
    enableColorSync = true,
    displayMode = 'front',
    rotationY = 0, // Default Y rotation
    tiltX = 0 // No tilt - model stands straight
}: FormalDressModelProps) {
    const [modelUri, setModelUri] = useState<string | null>(null);
    const [useSimpleModel, setUseSimpleModel] = useState(false);
    const [modelProcessed, setModelProcessed] = useState(false);
    const groupRef = useRef<any>(null);
    const gltf = modelUri ? useGLTF(modelUri) : null;

    // Load model URI - ONLY CHANGE: different model file
    useEffect(() => {
        const loadModelUri = async () => {
            try {
                // Try direct require first
                try {
                    const directUri = require('../assets/models/differentmessformal.glb');
                    setModelUri(directUri);
                    return;
                } catch (err) {
                    // Try Asset.fromModule as fallback
                }

                // Try Asset.fromModule
                try {
                    const modelAsset = Asset.fromModule(require('../assets/models/differentmessformal.glb'));
                    await modelAsset.downloadAsync();
                    const uri = modelAsset.localUri || modelAsset.uri;

                    if (uri) {
                        setModelUri(uri);
                        return;
                    }
                } catch (err) {
                    // Asset loading failed
                }

                // Fallback to simple model after 5 seconds
                setTimeout(() => {
                    setUseSimpleModel(true);
                }, 5000);

            } catch (error) {
                setUseSimpleModel(true);
            }
        };

        loadModelUri();
    }, []);

    // Process the loaded model with better texture handling and fixed transparency
    useEffect(() => {
        if (gltf && gltf.scene && !modelProcessed) {
            console.log('✅ Processing FormalDress GLTF model...');
            console.log('📊 FormalDress Model info:', {
                animations: gltf.animations?.length || 0,
                materials: Object.keys(gltf.materials || {}).length,
                nodes: Object.keys(gltf.nodes || {}).length
            });

            setModelProcessed(true);

            // Process materials with enhanced texture loading and fixed transparency
            gltf.scene.traverse((child: any) => {
                if (child.isMesh && child.material) {
                    const materials = Array.isArray(child.material) ? child.material : [child.material];

                    materials.forEach((material: any, index: number) => {
                        try {
                            const materialName = material?.name || 'unnamed';
                            const childName = child?.name || '';
                            console.log(`🎨 Processing FormalDress material ${index}:`, materialName);

                            // Enhanced material processing with mesh name context
                            processMaterialWithTextures(material, child, index, childName);
                        } catch (error) {
                            console.error('❌ Error processing FormalDress material:', error);
                            try {
                                applyFallbackMaterial(child, material, index, child?.name || '');
                            } catch (fallbackError) {
                                console.error('❌ FormalDress fallback material also failed:', fallbackError);
                            }
                        }
                    });

                    // Optimize for mobile performance - Fixed shadow settings
                    child.castShadow = false;
                    child.receiveShadow = false;
                    
                    // Fix geometry to prevent holes and transparency issues
                    if (child.geometry) {
                        child.geometry.computeVertexNormals();
                        child.geometry.computeBoundingBox();
                        child.geometry.computeBoundingSphere();
                    }
                }
            });

            // Fix model position and rotation to make it straight
            if (gltf.scene) {
                // Reset any existing rotations that might cause tilting
                gltf.scene.rotation.set(0, 0, 0);
                gltf.scene.position.set(0, 0, 0);
                gltf.scene.scale.set(1, 1, 1);
                
                // Ensure the model is properly centered
                gltf.scene.updateMatrixWorld(true);
            }

            console.log('✅ FormalDress model processing complete');
        }
    }, [gltf, modelProcessed]);

    // Apply dress colors when they change
    useEffect(() => {
        if (gltf && gltf.scene && modelProcessed && enableColorSync && dressColors) {
            console.log('🎨 Updating FormalDress colors:', dressColors);

            gltf.scene.traverse((child: any) => {
                if (child.isMesh && child.material) {
                    const materials = Array.isArray(child.material) ? child.material : [child.material];

                    materials.forEach((material: any, index: number) => {
                        const materialName = material?.name || 'unnamed';
                        const childName = child?.name || '';

                        // Apply new dress colors
                        const newColor = getIntelligentColor(materialName, index, childName);
                        if (material.color) {
                            material.color.copy(newColor);
                            material.needsUpdate = true;
                        }
                    });
                }
            });
        }
    }, [dressColors, gltf, modelProcessed, enableColorSync]);

    // Enhanced material processing function with fixed transparency handling
    const processMaterialWithTextures = (material: any, child: any, index: number, meshName: string) => {
        // Create enhanced material with better properties and fixed transparency
        const enhancedMaterial = new MeshStandardMaterial({
            name: material.name || `enhanced_material_${index}`,
            // Fix transparency issues
            transparent: false, // Default to false to prevent holes
            opacity: 1.0, // Full opacity
            alphaTest: 0.5, // Use alpha test instead of transparency
            side: FrontSide, // Use front side rendering to prevent see-through issues
            depthWrite: true, // Enable depth writing
            depthTest: true, // Enable depth testing
        });

        // Copy basic material properties with safety checks
        if (material.color) enhancedMaterial.color = material.color.clone();
        if (material.emissive) enhancedMaterial.emissive = material.emissive.clone();
        
        // Enhanced smoothness with proper settings to prevent holes
        enhancedMaterial.roughness = material.roughness !== undefined ? Math.max(0.1, material.roughness) : 0.4;
        enhancedMaterial.metalness = material.metalness !== undefined ? Math.min(0.8, material.metalness) : 0.1;
        
        // Only set transparency if the original material actually needs it
        if (material.transparent === true && material.opacity < 1.0) {
            enhancedMaterial.transparent = true;
            enhancedMaterial.opacity = Math.max(0.8, material.opacity); // Prevent too much transparency
            enhancedMaterial.alphaTest = 0.1; // Lower alpha test for transparent materials
        }

        // Handle textures with fallbacks
        if (material.map) {
            try {
                // Try to use existing texture
                enhancedMaterial.map = material.map;
                if (enhancedMaterial.map) {
                    enhancedMaterial.map.wrapS = RepeatWrapping;
                    enhancedMaterial.map.wrapT = RepeatWrapping;
                    enhancedMaterial.map.minFilter = LinearFilter;
                    enhancedMaterial.map.magFilter = LinearFilter;
                    enhancedMaterial.map.flipY = false; // Fix texture orientation
                }
                console.log(`✅ Applied existing texture to FormalDress material ${index} (${meshName})`);
            } catch (textureError) {
                console.log(`⚠️ FormalDress texture application failed for material ${index}, using solid color`);
                // Apply intelligent solid color based on material name AND mesh name
                enhancedMaterial.color = getIntelligentColor(material.name || '', index, meshName);
            }
        } else {
            // No texture available, use intelligent solid color
            enhancedMaterial.color = getIntelligentColor(material.name || '', index, meshName);
            console.log(`🎨 Applied solid color to FormalDress material ${index} (${meshName}):`, enhancedMaterial.color.getHexString());
        }

        // Handle normal maps with reduced intensity to prevent artifacts
        if (material.normalMap) {
            try {
                enhancedMaterial.normalMap = material.normalMap;
                enhancedMaterial.normalScale.set(0.3, 0.3); // Reduced normal intensity for smoother look
            } catch (error) {
                console.log(`⚠️ FormalDress normal map failed for material ${index}`);
            }
        }

        // Enhanced smoothness settings without creating holes
        enhancedMaterial.roughness = Math.max(0.2, enhancedMaterial.roughness); // Minimum roughness to prevent mirror effect
        enhancedMaterial.metalness = Math.min(0.5, enhancedMaterial.metalness); // Maximum metalness to prevent artifacts

        // Force material update
        enhancedMaterial.needsUpdate = true;

        // Apply the enhanced material
        if (Array.isArray(child.material)) {
            child.material[index] = enhancedMaterial;
        } else {
            child.material = enhancedMaterial;
        }

        console.log(`✅ Enhanced FormalDress material ${index} applied successfully to ${meshName}`);
    };

    // Fallback material application with fixed transparency
    const applyFallbackMaterial = (child: any, originalMaterial: any, index: number, meshName: string) => {
        const fallbackMaterial = new MeshStandardMaterial({
            color: getIntelligentColor(originalMaterial.name || '', index, meshName),
            transparent: false, // No transparency in fallback
            opacity: 1.0,
            roughness: 0.4,
            metalness: 0.1,
            side: FrontSide,
            depthWrite: true,
            depthTest: true,
        });

        if (Array.isArray(child.material)) {
            child.material[index] = fallbackMaterial;
        } else {
            child.material = fallbackMaterial;
        }

        console.log(`🔧 Applied fallback FormalDress material ${index} to ${meshName}`);
    };

    // Intelligent color assignment for formal dress parts
    const getIntelligentColor = (materialName: string, index: number, meshName: string = ''): Color => {
        const safeMaterialName = materialName || '';
        const safeMeshName = meshName || '';
        const name = safeMaterialName.toLowerCase();
        const mesh = safeMeshName.toLowerCase();

        console.log(`🎨 Assigning FormalDress color for material: "${safeMaterialName}" on mesh: "${safeMeshName}"`);

        // FORCE SKIN COLOR for Node meshes FIRST (before any other logic)
        if (mesh.toLowerCase().includes('node') || name.toLowerCase().includes('node')) {
            console.log('   → FORCED skin tone for Node mesh:', safeMeshName, safeMaterialName);
            return new Color(0xfdbcb4); // Natural skin tone
        }

        // Use dress colors if available and color sync is enabled
        if (enableColorSync && dressColors) {
            // FIRST: Apply pants color to leg dress/pants meshes (check this BEFORE dress)
            if (mesh.includes('legdress') || mesh.includes('pants') ||
                mesh.includes('legdress.001') || mesh.includes('legdress.002') || mesh.includes('legdress.003') ||
                name.includes('legdress') || name.includes('pants') ||
                name.includes('legdress.001') || name.includes('legdress.002') || name.includes('legdress.003')) {
                console.log('   → Applied PANTS color to legdress meshes:', dressColors.pants);

                // Always make pants different from shirt
                const shirtColor = new Color(dressColors.shirt);
                const pantsColor = new Color(dressColors.pants);

                if (shirtColor.getHexString() === pantsColor.getHexString()) {
                    // Colors are the same, make pants much darker
                    const darkerPants = pantsColor.clone().multiplyScalar(0.5); // 50% darker
                    console.log('   → Making pants darker than shirt:', darkerPants.getHexString());
                    return darkerPants;
                } else {
                    // Colors are different, use original pants color
                    console.log('   → Using original pants color:', pantsColor.getHexString());
                    return pantsColor;
                }
            }

            // SECOND: Apply shirt color to dress meshes AND hand meshes (but NOT legdress)
            if ((mesh.includes('dress') && !mesh.includes('legdress')) ||
                (mesh.includes('backdress') && !mesh.includes('legdress')) ||
                (name.includes('dress') && !name.includes('legdress')) ||
                (name.includes('backdress') && !name.includes('legdress')) ||
                mesh.includes('hand.003') || mesh.includes('hand.002') ||
                mesh.includes('hand.001') || mesh.includes('hand') ||
                name.includes('hand.003') || name.includes('hand.002') ||
                name.includes('hand.001') || name.includes('hand')) {
                console.log('   → Applied SHIRT color to dress/hands:', dressColors.shirt);
                return new Color(dressColors.shirt);
            }

            // Apply shoes color to shoes/heels meshes
            if (mesh.includes('hells') || mesh.includes('shoes') ||
                name.includes('hells') || name.includes('shoes')) {
                console.log('   → Applied shoes color:', dressColors.shoes);
                return new Color(dressColors.shoes);
            }
        }

        // Default colors for body parts (not affected by color combinations)
        // Check both mesh name and material name for skin parts (case-insensitive)
        if (mesh.includes('face') || mesh.includes('ear') || mesh.includes('hand') ||
            mesh.includes('plam') || mesh.includes('leg') || mesh.includes('node1024') ||
            mesh.includes('node1026') || mesh.includes('body') ||
            mesh.toLowerCase().includes('node1.006') || mesh.toLowerCase().includes('node0.006') ||
            name.toLowerCase().includes('node1.006') || name.toLowerCase().includes('node0.006') ||
            name.includes('face') || name.includes('ear') || name.includes('hand') ||
            name.includes('body') || name.includes('skin')) {
            console.log('   → Applied skin tone for body mesh:', meshName);
            return new Color(0xfdbcb4); // Natural skin tone
        }
        if (mesh.includes('hair')) {
            console.log('   → Applied black color for hair mesh');
            return new Color('#000000'); // Force black hair
        }
        if (mesh.includes('eye')) {
            console.log('   → Applied dark color for eyes mesh');
            return new Color('#000000'); // Black eyes
        }

        // Generate consistent color based on material name hash
        let hash = 0;
        const hashString = safeMaterialName || `material_${index}`;
        for (let i = 0; i < hashString.length; i++) {
            hash = ((hash << 5) - hash + hashString.charCodeAt(i)) & 0xffffffff;
        }

        // Generate pleasant colors (avoid pure black/white)
        const hue = Math.abs(hash) % 360;
        const saturation = 40 + (Math.abs(hash >> 8) % 40); // 40-80%
        const lightness = 30 + (Math.abs(hash >> 16) % 40); // 30-70%

        return new Color().setHSL(hue / 360, saturation / 100, lightness / 100);
    };

    // Calculate horizontal rotation (full 360 degrees)
    const getHorizontalRotation = () => {
        const baseRotation = displayMode === 'back' ? Math.PI : Math.PI; // Both front and back use Math.PI to face forward
        // Allow full 360-degree rotation - no limits
        return baseRotation + rotationY;
    };

    // Zoom functionality - controlled by props only (no user interaction)
    // User cannot zoom - only zoom out programmatically if needed

    // Return simple model if needed with same rotation props
    if (useSimpleModel) {
        console.log('🎨 Using SimpleModel fallback for FormalDress');
        return null; // No simple model fallback for now
    }

    // Show loading state
    if (!gltf || !modelProcessed) {
        return null; // Let the parent handle the loading state
    }

    // Fixed rotation approach - model rotates around its center point with proper positioning
    return (
        <group
            ref={groupRef}
            position={[-0.3, verticalOffset, 0]} // Shifted left
        >
            {/* Inner group for rotation - model rotates around its own center */}
            <group
                scale={[modelScale, modelScale, modelScale]}
                rotation={[0, getHorizontalRotation(), 0]} // No tilt, only horizontal rotation
                position={[0, 0, 0]} // Keep at origin for proper center rotation
            >
                {/* Model positioned at center - completely straight */}
                <primitive
                    object={gltf.scene}
                    rotation={[Math.PI + 0.25, 0, 0]} // Flip + more forward tilt
                    position={[0, -0.1, 0]} // Slightly lower to center better
                    scale={[1, 1, 1]} // Ensure uniform scaling
                />
            </group>
        </group>
    );
}