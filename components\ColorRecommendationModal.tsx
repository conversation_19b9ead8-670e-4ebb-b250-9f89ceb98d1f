import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
  ScrollView,
  PanResponder,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

interface OutfitRecommendation {
  outfitName: string;
  shirt: {
    color: string;
    hex: string;
    reason: string;
  };
  pants: {
    color: string;
    hex: string;
    reason: string;
  };
  shoes: {
    color: string;
    hex: string;
    reason: string;
  };
  overallReason: string;
}

interface ColorRecommendationModalProps {
  visible: boolean;
  onClose: () => void;
  outfit: OutfitRecommendation | null;
  seasonalType?: string;
  advice?: string;
}

export const ColorRecommendationModal: React.FC<ColorRecommendationModalProps> = ({
  visible,
  onClose,
  outfit,
  seasonalType,
  advice,
}) => {
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Slide up animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Slide down animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  // Pan responder for swipe down to close
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      return gestureState.dy > 0 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
    },
    onPanResponderMove: (evt, gestureState) => {
      if (gestureState.dy > 0) {
        slideAnim.setValue(gestureState.dy);
      }
    },
    onPanResponderRelease: (evt, gestureState) => {
      if (gestureState.dy > 100) {
        // Close modal if swiped down more than 100px
        onClose();
      } else {
        // Snap back to open position
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  if (!outfit) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      {/* Backdrop */}
      <Animated.View 
        style={[
          styles.backdrop,
          { opacity: backdropOpacity }
        ]}
      >
        <TouchableOpacity 
          style={styles.backdropTouchable}
          onPress={onClose}
          activeOpacity={1}
        />
      </Animated.View>

      {/* Modal Content */}
      <Animated.View
        style={[
          styles.modalContainer,
          {
            transform: [{ translateY: slideAnim }],
          },
        ]}
        {...panResponder.panHandlers}
      >
        {/* Handle Bar */}
        <View style={styles.handleBar} />

        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Ionicons name="shirt" size={24} color="#e91e63" />
            <Text style={styles.headerTitle}>{outfit.outfitName}</Text>
          </View>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#64748b" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Color Preview Section */}
          <View style={styles.colorPreviewSection}>
            <Text style={styles.sectionTitle}>Color Combination</Text>
            <View style={styles.colorPreviewContainer}>
              <View style={styles.colorPreviewItem}>
                <View style={[styles.colorCircle, { backgroundColor: outfit.shirt.hex }]} />
                <Text style={styles.colorLabel}>Shirt</Text>
                <Text style={styles.colorName}>{outfit.shirt.color}</Text>
                <Text style={styles.colorHex}>{outfit.shirt.hex}</Text>
              </View>
              <View style={styles.colorPreviewItem}>
                <View style={[styles.colorCircle, { backgroundColor: outfit.pants.hex }]} />
                <Text style={styles.colorLabel}>Pants</Text>
                <Text style={styles.colorName}>{outfit.pants.color}</Text>
                <Text style={styles.colorHex}>{outfit.pants.hex}</Text>
              </View>
              <View style={styles.colorPreviewItem}>
                <View style={[styles.colorCircle, { backgroundColor: outfit.shoes.hex }]} />
                <Text style={styles.colorLabel}>Shoes</Text>
                <Text style={styles.colorName}>{outfit.shoes.color}</Text>
                <Text style={styles.colorHex}>{outfit.shoes.hex}</Text>
              </View>
            </View>
          </View>

          {/* Overall Reason */}
          <View style={styles.reasonSection}>
            <View style={styles.reasonHeader}>
              <Ionicons name="bulb" size={20} color="#f39c12" />
              <Text style={styles.sectionTitle}>Why This Combination Works</Text>
            </View>
            <Text style={styles.reasonText}>{outfit.overallReason}</Text>
          </View>

          {/* Individual Color Reasons */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Color Details</Text>
            
            <View style={styles.colorDetailCard}>
              <View style={styles.colorDetailHeader}>
                <View style={[styles.smallColorCircle, { backgroundColor: outfit.shirt.hex }]} />
                <Text style={styles.colorDetailTitle}>Shirt - {outfit.shirt.color}</Text>
              </View>
              <Text style={styles.colorDetailReason}>{outfit.shirt.reason}</Text>
            </View>

            <View style={styles.colorDetailCard}>
              <View style={styles.colorDetailHeader}>
                <View style={[styles.smallColorCircle, { backgroundColor: outfit.pants.hex }]} />
                <Text style={styles.colorDetailTitle}>Pants - {outfit.pants.color}</Text>
              </View>
              <Text style={styles.colorDetailReason}>{outfit.pants.reason}</Text>
            </View>

            <View style={styles.colorDetailCard}>
              <View style={styles.colorDetailHeader}>
                <View style={[styles.smallColorCircle, { backgroundColor: outfit.shoes.hex }]} />
                <Text style={styles.colorDetailTitle}>Shoes - {outfit.shoes.color}</Text>
              </View>
              <Text style={styles.colorDetailReason}>{outfit.shoes.reason}</Text>
            </View>
          </View>

          {/* Seasonal Type & Advice */}
          {(seasonalType || advice) && (
            <View style={styles.adviceSection}>
              {seasonalType && (
                <View style={styles.seasonalTypeCard}>
                  <Ionicons name="leaf" size={20} color="#27ae60" />
                  <Text style={styles.seasonalTypeText}>Your Seasonal Type: {seasonalType}</Text>
                </View>
              )}
              
              {advice && (
                <View style={styles.adviceCard}>
                  <View style={styles.adviceHeader}>
                    <Ionicons name="information-circle" size={20} color="#3b82f6" />
                    <Text style={styles.adviceTitle}>Personal Color Advice</Text>
                  </View>
                  <Text style={styles.adviceText}>{advice}</Text>
                </View>
              )}
            </View>
          )}

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
  },
  backdropTouchable: {
    flex: 1,
  },
  modalContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: screenHeight * 0.85,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  handleBar: {
    width: 40,
    height: 4,
    backgroundColor: '#d1d5db',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1f2937',
    marginLeft: 12,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  colorPreviewSection: {
    marginTop: 20,
    marginBottom: 24,
  },
  colorPreviewContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    padding: 20,
  },
  colorPreviewItem: {
    alignItems: 'center',
  },
  colorCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
    borderWidth: 3,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  colorLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  colorName: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 2,
  },
  colorHex: {
    fontSize: 11,
    color: '#9ca3af',
    fontFamily: 'monospace',
  },
  reasonSection: {
    marginBottom: 24,
  },
  reasonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  reasonText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    backgroundColor: '#fef3c7',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b',
  },
  detailsSection: {
    marginBottom: 24,
  },
  colorDetailCard: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  colorDetailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  smallColorCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 12,
    borderWidth: 2,
    borderColor: '#fff',
  },
  colorDetailTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  colorDetailReason: {
    fontSize: 14,
    color: '#4b5563',
    lineHeight: 20,
  },
  adviceSection: {
    marginBottom: 24,
  },
  seasonalTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ecfdf5',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#a7f3d0',
  },
  seasonalTypeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#065f46',
    marginLeft: 12,
  },
  adviceCard: {
    backgroundColor: '#eff6ff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#bfdbfe',
  },
  adviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  adviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e40af',
    marginLeft: 12,
  },
  adviceText: {
    fontSize: 14,
    color: '#1e40af',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});
