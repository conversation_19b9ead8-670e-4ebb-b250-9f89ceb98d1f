import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

export const Credits: React.FC = () => {
  const openLink = async (url: string) => {
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.log('Error opening link:', error);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Ionicons name="people" size={40} color="#fff" />
          <Text style={styles.headerTitle}>Credits & Attributions</Text>
          <Text style={styles.headerSubtitle}>Acknowledging our amazing contributors</Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📦 3D Assets Used</Text>
          <Text style={styles.sectionText}>
            Our app features beautiful 3D models created by talented artists. We're grateful for their contributions to the creative community.
          </Text>
        </View>

        {/* Realistic Women Model */}
        <View style={styles.assetCard}>
          <View style={styles.assetHeader}>
            <Ionicons name="woman" size={24} color="#667eea" />
            <Text style={styles.assetTitle}>"Realistic Women" by Michal_K</Text>
          </View>
          <Text style={styles.assetDescription}>
            Licensed under Creative Commons Attribution 4.0 (CC BY 4.0)
          </Text>
          <Text style={styles.modificationsText}>
            <Text style={styles.modificationsLabel}>Modifications: </Text>
            Hair, bag, jacket, skin tone, pants, and boot colors dynamically updated using AI-based personalization (Gemini).
          </Text>
          <TouchableOpacity 
            style={styles.linkButton}
            onPress={() => openLink('https://sketchfab.com/3d-models/realistic-women-b1044722512546e9b3e49c0ec10f0d18')}
          >
            <Ionicons name="link" size={16} color="#667eea" />
            <Text style={styles.linkText}>View Original Model</Text>
          </TouchableOpacity>
        </View>

        {/* Formal Male Model */}
        <View style={styles.assetCard}>
          <View style={styles.assetHeader}>
            <Ionicons name="man" size={24} color="#667eea" />
            <Text style={styles.assetTitle}>"Formal Male" by JohnDoe_3D</Text>
          </View>
          <Text style={styles.assetDescription}>
            Licensed under Creative Commons Attribution 4.0 (CC BY 4.0)
          </Text>
          <Text style={styles.modificationsText}>
            <Text style={styles.modificationsLabel}>Modifications: </Text>
            Outfit recolored based on AI style recommendations. Skin and hair tone personalized.
          </Text>
          <TouchableOpacity 
            style={styles.linkButton}
            onPress={() => openLink('https://sketchfab.com/models/formal-male-example')}
          >
            <Ionicons name="link" size={16} color="#667eea" />
            <Text style={styles.linkText}>View Original Model</Text>
          </TouchableOpacity>
        </View>

        {/* Standing Pose Female Model */}
        <View style={styles.assetCard}>
          <View style={styles.assetHeader}>
            <Ionicons name="body" size={24} color="#667eea" />
            <Text style={styles.assetTitle}>"Standing Pose Female" by Studio3D</Text>
          </View>
          <Text style={styles.assetDescription}>
            Licensed under Creative Commons Attribution 4.0 (CC BY 4.0)
          </Text>
          <Text style={styles.modificationsText}>
            <Text style={styles.modificationsLabel}>Modifications: </Text>
            Dress color, makeup tone, and hair style updated dynamically using AI recommendations.
          </Text>
          <TouchableOpacity 
            style={styles.linkButton}
            onPress={() => openLink('https://sketchfab.com/models/pose-female')}
          >
            <Ionicons name="link" size={16} color="#667eea" />
            <Text style={styles.linkText}>View Original Model</Text>
          </TouchableOpacity>
        </View>

        {/* License Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📄 License Information</Text>
          <Text style={styles.sectionText}>
            All 3D models used in this application are licensed under Creative Commons Attribution 4.0 (CC BY 4.0). This means:
          </Text>
          <View style={styles.bulletPoints}>
            <Text style={styles.bulletPoint}>• You are free to share and adapt the material</Text>
            <Text style={styles.bulletPoint}>• Attribution must be given to the original creator</Text>
            <Text style={styles.bulletPoint}>• No additional restrictions may be applied</Text>
            <Text style={styles.bulletPoint}>• Commercial use is permitted</Text>
          </View>
        </View>

        {/* AI Technology */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🤖 AI Technology</Text>
          <Text style={styles.sectionText}>
            Our color personalization system is powered by Google's Gemini AI, providing intelligent color recommendations based on facial analysis and style preferences.
          </Text>
        </View>

        {/* Thank You */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🙏 Thank You</Text>
          <Text style={styles.sectionText}>
            We extend our heartfelt gratitude to all the 3D artists, developers, and the open-source community who make applications like this possible. Your creativity and generosity inspire innovation.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    paddingBottom: 30,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 10,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#e2e8f0',
    marginTop: 5,
    textAlign: 'center',
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 10,
  },
  sectionText: {
    fontSize: 15,
    color: '#34495e',
    lineHeight: 22,
  },
  assetCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  assetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  assetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    marginLeft: 10,
    flex: 1,
  },
  assetDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 10,
    fontStyle: 'italic',
  },
  modificationsText: {
    fontSize: 14,
    color: '#34495e',
    lineHeight: 20,
    marginBottom: 15,
  },
  modificationsLabel: {
    fontWeight: 'bold',
    color: '#1e293b',
  },
  linkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  linkText: {
    fontSize: 14,
    color: '#667eea',
    marginLeft: 5,
    fontWeight: '500',
  },
  bulletPoints: {
    marginTop: 10,
  },
  bulletPoint: {
    fontSize: 15,
    color: '#34495e',
    lineHeight: 22,
    marginBottom: 5,
  },
});
