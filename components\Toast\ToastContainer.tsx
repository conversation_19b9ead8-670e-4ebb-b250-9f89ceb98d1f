import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Animated,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  PanResponder,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { responsiveService } from '../../utils/responsive';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export interface ToastData {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onPress?: () => void;
  onDismiss?: () => void;
}

interface ToastItemProps {
  toast: ToastData;
  onDismiss: (id: string) => void;
}

const ToastItem: React.FC<ToastItemProps> = ({ toast, onDismiss }) => {
  const translateY = useRef(new Animated.Value(-100)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Slide in animation
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss
    if (toast.duration !== 0) {
      const timer = setTimeout(() => {
        dismissToast();
      }, toast.duration || 4000);

      return () => clearTimeout(timer);
    }
  }, []);

  const dismissToast = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: -100,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(toast.id);
      toast.onDismiss?.();
    });
  };

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      return Math.abs(gestureState.dx) > 20;
    },
    onPanResponderMove: (_, gestureState) => {
      translateX.setValue(gestureState.dx);
    },
    onPanResponderRelease: (_, gestureState) => {
      if (Math.abs(gestureState.dx) > SCREEN_WIDTH * 0.3) {
        // Swipe to dismiss
        Animated.timing(translateX, {
          toValue: gestureState.dx > 0 ? SCREEN_WIDTH : -SCREEN_WIDTH,
          duration: 200,
          useNativeDriver: true,
        }).start(() => dismissToast());
      } else {
        // Snap back
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  const getToastStyle = () => {
    const baseStyle = {
      backgroundColor: '#fff',
      borderLeftWidth: 4,
      borderLeftColor: '#007AFF',
    };

    switch (toast.type) {
      case 'success':
        return {
          ...baseStyle,
          backgroundColor: '#f0f9ff',
          borderLeftColor: '#10b981',
        };
      case 'error':
        return {
          ...baseStyle,
          backgroundColor: '#fef2f2',
          borderLeftColor: '#ef4444',
        };
      case 'warning':
        return {
          ...baseStyle,
          backgroundColor: '#fffbeb',
          borderLeftColor: '#f59e0b',
        };
      case 'info':
        return {
          ...baseStyle,
          backgroundColor: '#f0f9ff',
          borderLeftColor: '#3b82f6',
        };
      default:
        return baseStyle;
    }
  };

  const getIconName = () => {
    switch (toast.type) {
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'close-circle';
      case 'warning':
        return 'warning';
      case 'info':
        return 'information-circle';
      default:
        return 'information-circle';
    }
  };

  const getIconColor = () => {
    switch (toast.type) {
      case 'success':
        return '#10b981';
      case 'error':
        return '#ef4444';
      case 'warning':
        return '#f59e0b';
      case 'info':
        return '#3b82f6';
      default:
        return '#3b82f6';
    }
  };

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        getToastStyle(),
        {
          transform: [{ translateY }, { translateX }],
          opacity,
        },
      ]}
      {...panResponder.panHandlers}
    >
      <TouchableOpacity
        style={styles.toastContent}
        onPress={toast.onPress}
        activeOpacity={toast.onPress ? 0.7 : 1}
      >
        <View style={styles.iconContainer}>
          <Ionicons
            name={getIconName() as any}
            size={responsiveService.iconSize(24)}
            color={getIconColor()}
          />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { fontSize: responsiveService.fontSize(16) }]}>
            {toast.title}
          </Text>
          {toast.message && (
            <Text style={[styles.message, { fontSize: responsiveService.fontSize(14) }]}>
              {toast.message}
            </Text>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.closeButton}
          onPress={dismissToast}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name="close"
            size={responsiveService.iconSize(20)}
            color="#6b7280"
          />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
};

interface ToastContainerProps {
  toasts: ToastData[];
  onDismiss: (id: string) => void;
}

export const ToastContainer: React.FC<ToastContainerProps> = ({ toasts, onDismiss }) => {
  return (
    <View style={styles.container} pointerEvents="box-none">
      {toasts.map((toast) => (
        <ToastItem key={toast.id} toast={toast} onDismiss={onDismiss} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: responsiveService.spacing(50),
    left: responsiveService.spacing(16),
    right: responsiveService.spacing(16),
    zIndex: 9999,
  },
  toastContainer: {
    marginBottom: responsiveService.spacing(8),
    borderRadius: responsiveService.spacing(12),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  toastContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: responsiveService.spacing(16),
  },
  iconContainer: {
    marginRight: responsiveService.spacing(12),
    marginTop: responsiveService.spacing(2),
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    color: '#1f2937',
    lineHeight: 20,
  },
  message: {
    color: '#6b7280',
    marginTop: responsiveService.spacing(4),
    lineHeight: 18,
  },
  closeButton: {
    marginLeft: responsiveService.spacing(8),
    padding: responsiveService.spacing(4),
  },
});
