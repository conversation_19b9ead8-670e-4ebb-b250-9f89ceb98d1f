// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add support for GLB and GLTF files
config.resolver.assetExts.push('glb', 'gltf', 'bin', 'jpg', 'jpeg', 'png');

// Add path alias support
config.resolver.alias = {
  '@': path.resolve(__dirname, './'),
};

// Optional: Add transformer for better asset handling
config.transformer = {
  ...config.transformer,
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
};

// Increase the maximum bundle size if needed
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;