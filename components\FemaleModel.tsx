import { useGLTF } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { Asset } from 'expo-asset';
import React, { useEffect, useRef, useState } from 'react';
import { Color, MeshStandardMaterial } from 'three';
import { UserColors } from '../contexts/ColorContext';
import { ColorCombination } from '../services/colorService';

interface FemaleModelProps {
  colorCombination?: ColorCombination;
  userColors?: UserColors | null;
  rotationX?: number;
  rotationY?: number;
  isManualRotation?: boolean;
  scale?: number;
  position?: [number, number, number];
  [key: string]: any;
}

export function FemaleModel(props: FemaleModelProps) {
  const { 
    colorCombination, 
    userColors, 
    rotationX = 0, 
    rotationY = 0, 
    isManualRotation = false,
    scale = 1,
    position = [0, 0, 0],
    ...otherProps 
  } = props;
  const [modelUri, setModelUri] = useState<string | null>(null);
  const groupRef = useRef<any>(null);
  
  // Load the female model
  useEffect(() => {
    const loadModel = async () => {
      try {
        // Try direct require first
        try {
          const directUri = require('../assets/models/female.glb');
          setModelUri(directUri);
          return;
        } catch (err:any) {
          console.log('Direct require failed, trying Asset.fromModule',err);
        }

        const femaleAsset = Asset.fromModule(require('../assets/models/female.glb'));
        await femaleAsset.downloadAsync();
        const uri = femaleAsset.localUri || femaleAsset.uri;
        if (uri) {
          setModelUri(uri);
        }
      } catch (error) {
        console.log('Error loading female model:', error);
      }
    };
    
    loadModel();
  }, []);
  
  const gltf = modelUri ? useGLTF(modelUri) : null;
  const { nodes, materials } = gltf || { nodes: {}, materials: {} };

  // Color mapping function with dynamic color combinations
  const getColorForMesh = (meshName: string): Color => {
    const name = meshName.toLowerCase();

    // Skin parts
    if (
      name.includes('face') || name.includes('node1') || name.includes('node1001') ||
      name.includes('node1002') || name.includes('node1003') || name.includes('node1004') ||
      name.includes('righthand') || name.includes('lefthand') || name.includes('rightleg') ||
      name.includes('leftleg')
    ) {
      return new Color(0xfdbcb4); // Natural skin tone
    }

    // Hair - use user's actual hair color from face analysis
    if (name.includes('hair')) {
      const hairColor = userColors?.hairColor?.hex || '#8b4513';
      return new Color(hairColor);
    }

    // Eyes
    if (name.includes('righteye') || name.includes('lefteye')) {
      return new Color(0x2e4057); // Blue eyes
    }

    // Dress front - use shirt color from color combination
    if (name.includes('frontdress')) {
      const frontColor = colorCombination?.shirt || '#b57edc';
      return new Color(frontColor);
    }

    // Back cloth - use pants color from color combination
    if (name.includes('backcloth')) {
      const backColor = colorCombination?.shirt || '#b57edc';
      return new Color(backColor);
    }

    if (name.includes('righthandlelittlecloth')) {
      const righthandlelittlecloth = colorCombination?.shirt || '#b57edc';
      return new Color(righthandlelittlecloth);
    }

    if (name.includes('lefthandlittlecloth')) {
      const lefthandlittlecloth = colorCombination?.shirt || '#b57edc';
      return new Color(lefthandlittlecloth);
    }

    // Palm accessories (WHITE)
    if (name.includes('righthandpam') || name.includes('lefthandpam')) {
      return new Color(0xffffff);
    }

    // Heels - use shoes color from color combination
    if (name.includes('rightlegheel') || name.includes('leftlegheel')) {
      const heelColor = colorCombination?.shoes || '#000000';
      return new Color(heelColor);
    }

    // Accessories (GOLD)
    if (name.includes('node1015')) {
      return new Color(0xffd700); // Gold
    }

    // Default pink
    return new Color(0xe91e63);
  };

  // Apply colors to materials
  useEffect(() => {
    if (gltf && gltf.scene) {
      gltf.scene.traverse((child: any) => {
        if (child.isMesh && child.material) {
          const meshName = child.name || child.geometry?.name || '';
          const color = getColorForMesh(meshName);

          // Handle array of materials or single material
          const materials = Array.isArray(child.material) ? child.material : [child.material];

          materials.forEach((material: any) => {
            if (material.color) {
              material.color.copy(color);
              material.roughness = 0.8;
              material.metalness = 0.1;
              material.needsUpdate = true;
            } else {
              const newMaterial = new MeshStandardMaterial({
                color: color,
                roughness: 0.8,
                metalness: 0.1,
              });
              child.material = newMaterial;
            }
          });

          // Optimize for performance
          child.castShadow = false;
          child.receiveShadow = false;
        }
      });
    }
  }, [gltf, colorCombination, userColors]);
  
  // HORIZONTAL ONLY rotation handling
  useFrame(() => {
    if (groupRef.current) {
      // CRITICAL: Only allow horizontal (Y-axis) rotation
      groupRef.current.rotation.y = rotationY;
      groupRef.current.rotation.x = 0; // Lock vertical rotation
      groupRef.current.rotation.z = 0; // Lock roll rotation
    }
  });

  // Return null if model not loaded
  if (!gltf || !nodes || Object.keys(nodes).length === 0) {
    return null;
  }

  return (
    <group
      ref={groupRef}
      {...otherProps}
      dispose={null}
      scale={[scale, scale, scale]}
      position={position}
      rotation={[0, 0, 0]} // CRITICAL: Start with no rotation
    >
      <primitive
        object={gltf.scene}
        rotation={[0, 0, 0]} // CRITICAL: Ensure primitive starts with no rotation
      />
    </group>
  );
}

// Preload will be handled by Asset loading