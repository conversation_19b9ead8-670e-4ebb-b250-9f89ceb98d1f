export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
  score: number; // 0-100
}

export interface PasswordValidationOptions {
  minLength?: number;
  maxLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
  checkCommonPasswords?: boolean;
}

// Common weak passwords to avoid
const COMMON_PASSWORDS = [
  'password', 'password123', '123456', '123456789', 'qwerty', 'abc123',
  'password1', 'admin', 'letmein', 'welcome', 'monkey', 'dragon',
  '111111', '123123', 'sunshine', 'master', 'shadow', 'football',
  'baseball', 'superman', 'michael', 'jordan', 'harley', 'ranger'
];

// Special characters allowed
const SPECIAL_CHARS = '!@#$%^&*()_+-=[]{}|;:,.<>?';

export const validatePassword = (
  password: string,
  options: PasswordValidationOptions = {}
): PasswordValidationResult => {
  const {
    minLength = 6,
    maxLength = 128,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true,
    checkCommonPasswords = true
  } = options;

  const errors: string[] = [];
  let score = 0;

  // Length validation
  if (password.length < minLength) {
    errors.push(`🔒 Password bahut chhota hai! Kam se kam ${minLength} characters chahiye! 📏`);
  } else {
    score += 20;
  }

  if (password.length > maxLength) {
    errors.push(`📏 Password bahut lamba hai! Maximum ${maxLength} characters allowed! ✂️`);
  }

  // Uppercase validation
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('💪 Password mein uppercase letter add kar! Strong banao! 🔤');
  } else if (/[A-Z]/.test(password)) {
    score += 15;
  }

  // Lowercase validation
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('🔡 Lowercase letters bhi chahiye! Mix kar ke banao! 📝');
  } else if (/[a-z]/.test(password)) {
    score += 15;
  }

  // Numbers validation
  if (requireNumbers && !/[0-9]/.test(password)) {
    errors.push('🔢 Numbers bhi daal password mein! Security ke liye! #️⃣');
  } else if (/[0-9]/.test(password)) {
    score += 15;
  }

  // Special characters validation
  if (requireSpecialChars && !new RegExp(`[${SPECIAL_CHARS.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)) {
    errors.push('⚡ Special characters add kar! (!@#$%^&*) Security badh jaegi! 🛡️');
  } else if (new RegExp(`[${SPECIAL_CHARS.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)) {
    score += 15;
  }

  // Common passwords check
  if (checkCommonPasswords && COMMON_PASSWORDS.includes(password.toLowerCase())) {
    errors.push('🚫 Ye password bahut common hai! Unique banao bhai! 🎯');
    score -= 30;
  }

  // Additional scoring based on length
  if (password.length >= 8) score += 10;
  if (password.length >= 12) score += 10;

  // Bonus for character variety
  const charTypes = [
    /[A-Z]/.test(password),
    /[a-z]/.test(password),
    /[0-9]/.test(password),
    new RegExp(`[${SPECIAL_CHARS.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)
  ].filter(Boolean).length;

  if (charTypes >= 3) score += 10;
  if (charTypes === 4) score += 10;

  // Ensure score is within bounds
  score = Math.max(0, Math.min(100, score));

  // Determine strength
  let strength: 'weak' | 'medium' | 'strong';
  if (score < 40) {
    strength = 'weak';
  } else if (score < 70) {
    strength = 'medium';
  } else {
    strength = 'strong';
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength,
    score
  };
};

// Simplified validation for login (existing passwords)
export const validateLoginPassword = (password: string): PasswordValidationResult => {
  return validatePassword(password, {
    minLength: 1, // Don't enforce strict rules for existing passwords
    requireUppercase: false,
    requireLowercase: false,
    requireNumbers: false,
    requireSpecialChars: false,
    checkCommonPasswords: false
  });
};

// Get password strength color
export const getPasswordStrengthColor = (strength: 'weak' | 'medium' | 'strong'): string => {
  switch (strength) {
    case 'weak':
      return '#ef4444'; // Red
    case 'medium':
      return '#f59e0b'; // Orange
    case 'strong':
      return '#10b981'; // Green
    default:
      return '#6b7280'; // Gray
  }
};

// Get password strength message
export const getPasswordStrengthMessage = (strength: 'weak' | 'medium' | 'strong'): string => {
  switch (strength) {
    case 'weak':
      return '😰 Password weak hai! Aur strong banao! 💪';
    case 'medium':
      return '😊 Password theek hai! Aur bhi better kar sakte ho! 👍';
    case 'strong':
      return '🔥 Wah! Password ekdum strong hai! Perfect! 💯';
    default:
      return '';
  }
};

// Debounced validation for real-time feedback
export const createDebouncedValidator = (
  callback: (result: PasswordValidationResult) => void,
  delay: number = 500
) => {
  let timeoutId: NodeJS.Timeout;

  return (password: string, options?: PasswordValidationOptions) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      const result = validatePassword(password, options);
      callback(result);
    }, delay);
  };
};
