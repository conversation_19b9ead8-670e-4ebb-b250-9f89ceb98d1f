import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { femaleModelCategories, ModelCategory } from '../data/femaleModelCategories';

const { width } = Dimensions.get('window');
const cardWidth = (width - 48) / 2; // 2 cards per row with margins

interface FemaleModelSelectorProps {
  onCategorySelect: (category: ModelCategory) => void;
}

export const FemaleModelSelector: React.FC<FemaleModelSelectorProps> = ({
  onCategorySelect,
}) => {
  const handleCategoryPress = (category: ModelCategory) => {
    if (category.available) {
      onCategorySelect(category);
    }
  };

  const renderCategoryCard = (category: ModelCategory) => {
    return (
      <TouchableOpacity
        key={category.id}
        style={[
          styles.categoryCard,
          !category.available && styles.disabledCard,
        ]}
        onPress={() => handleCategoryPress(category)}
        activeOpacity={category.available ? 0.8 : 1}
      >
        {/* Icon */}
        <View style={styles.iconContainer}>
          {category.available ? (
            <Ionicons name="woman" size={40} color="#e91e63" />
          ) : (
            <Ionicons name="time-outline" size={40} color="#9ca3af" />
          )}
        </View>

        {/* Category Name */}
        <Text style={[
          styles.categoryName,
          !category.available && styles.disabledText
        ]}>
          {category.name}
        </Text>

        {/* Status */}
        {category.available ? (
          <Text style={styles.availableText}>Available</Text>
        ) : (
          <Text style={styles.comingSoonText}>Coming Soon</Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Choose Your Style</Text>
      </View>

      {/* Categories Grid */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.categoriesGrid}>
          {femaleModelCategories.map(renderCategoryCard)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: cardWidth,
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  disabledCard: {
    backgroundColor: '#f1f5f9',
    opacity: 0.6,
  },
  iconContainer: {
    marginBottom: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  disabledText: {
    color: '#9ca3af',
  },
  availableText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#059669',
  },
  comingSoonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#d97706',
  },
});
