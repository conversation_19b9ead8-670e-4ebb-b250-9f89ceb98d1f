// Backend endpoint implementation for /face/analyze-url
// Add this to your Express.js backend

const express = require('express');
const fetch = require('node-fetch'); // or use built-in fetch in Node.js 18+
const multer = require('multer');

// Middleware for authentication
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  // Verify your JWT token here
  // jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
  //   if (err) return res.status(403).json({ success: false, message: 'Invalid token' });
  //   req.user = user;
  //   next();
  // });
  
  // For now, just continue (replace with your actual JWT verification)
  next();
};

// New endpoint that accepts image URL
app.post('/api/face/analyze-url', authenticateToken, async (req, res) => {
  try {
    console.log('🔗 Received face analysis request with URL');
    console.log('📝 Request body:', JSON.stringify(req.body, null, 2));

    const { imageUrl, publicId, originalFileName, metadata } = req.body;

    // Validate required fields
    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    // Validate URL format
    try {
      new URL(imageUrl);
    } catch (urlError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid image URL format'
      });
    }

    console.log('📥 Downloading image from URL:', imageUrl);

    // Download image from URL
    let imageBuffer;
    let contentType;
    try {
      const imageResponse = await fetch(imageUrl);
      
      if (!imageResponse.ok) {
        throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
      }

      contentType = imageResponse.headers.get('content-type');
      
      // Validate content type
      if (!contentType || !contentType.startsWith('image/')) {
        throw new Error('URL does not point to a valid image');
      }

      imageBuffer = await imageResponse.buffer();
      console.log('✅ Image downloaded successfully:', {
        size: imageBuffer.length,
        contentType: contentType
      });

    } catch (downloadError) {
      console.error('❌ Failed to download image:', downloadError);
      return res.status(400).json({
        success: false,
        message: `Failed to download image: ${downloadError.message}`
      });
    }

    // Process the image for face analysis
    console.log('🔍 Starting face analysis...');
    
    try {
      // Replace this with your actual face analysis logic
      const analysisResult = await processFaceAnalysis({
        buffer: imageBuffer,
        mimetype: contentType,
        originalname: originalFileName || 'image-from-url.jpg'
      });

      // Create analysis record in database
      const analysisRecord = {
        _id: generateAnalysisId(), // Your ID generation logic
        userId: req.user?.id, // From JWT token
        imageUrl: imageUrl,
        publicId: publicId,
        originalFileName: originalFileName,
        faceDetected: analysisResult.faceDetected,
        colors: analysisResult.colors,
        facialFeatures: analysisResult.facialFeatures,
        confidence: analysisResult.confidence,
        processingTime: analysisResult.processingTime,
        metadata: {
          ...metadata,
          imageSource: 'url',
          downloadedAt: new Date().toISOString()
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save to database
      // await FaceAnalysis.create(analysisRecord);

      console.log('✅ Face analysis completed successfully');
      console.log('📊 Analysis ID:', analysisRecord._id);

      res.json({
        success: true,
        message: 'Face analysis completed successfully',
        data: analysisRecord
      });

    } catch (analysisError) {
      console.error('❌ Face analysis failed:', analysisError);
      res.status(500).json({
        success: false,
        message: `Face analysis failed: ${analysisError.message}`
      });
    }

  } catch (error) {
    console.error('❌ Server error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Helper function - replace with your actual face analysis implementation
async function processFaceAnalysis(imageFile) {
  // This is where you'd integrate with your face analysis service
  // (OpenCV, AWS Rekognition, Google Vision API, etc.)
  
  // Mock implementation for example
  return {
    faceDetected: true,
    confidence: 0.95,
    processingTime: 1500,
    colors: {
      hairColor: {
        primary: 'brown',
        hex: '#8B4513',
        rgb: { r: 139, g: 69, b: 19 },
        confidence: 0.85
      },
      skinTone: {
        primary: 'medium',
        hex: '#D2B48C',
        rgb: { r: 210, g: 180, b: 140 },
        confidence: 0.90
      },
      eyeColor: {
        primary: 'brown',
        hex: '#654321',
        rgb: { r: 101, g: 67, b: 33 },
        confidence: 0.80
      },
      lipColor: {
        primary: 'natural',
        hex: '#CD5C5C',
        rgb: { r: 205, g: 92, b: 92 },
        confidence: 0.75
      }
    },
    facialFeatures: {
      faceShape: 'oval',
      eyeShape: 'almond',
      eyeDistance: 'normal',
      eyebrowShape: 'arched',
      noseShape: 'straight',
      lipShape: 'full'
    }
  };
}

// Helper function - replace with your actual ID generation
function generateAnalysisId() {
  return 'analysis_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

module.exports = { authenticateToken };
