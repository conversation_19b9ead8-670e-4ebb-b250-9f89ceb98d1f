export interface ModelCategory {
  id: string;
  name: string;
  description: string;
  image: string;
  available: boolean;
  comingSoon?: boolean;
}

export const femaleModelCategories: ModelCategory[] = [
  {
    id: 'cheongsam',
    name: 'Cheongsam',
    description: 'Traditional Chinese dress with elegant silhouette',
    image: 'cheongsam-preview.jpg',
    available: true,
    comingSoon: false,
  },
  {
    id: 'Women Formal Dress',
    name: 'Women Formal Dress',
    description: 'Elegant formal dress for special occasions',
    image: 'onepice-preview.jpg',
    available: true,
    comingSoon: false,
  },
  {
    id: 'casual',
    name: 'Casual Wear',
    description: 'Comfortable everyday clothing styles with Gemini AI colors',
    image: 'casual-preview.jpg',
    available: true,
    comingSoon: false,
  },
  {
    id: 'business',
    name: 'Business Attire',
    description: 'Professional clothing for work environment',
    image: 'business-preview.jpg',
    available: false,
    comingSoon: true,
  },
  {
    id: 'traditional',
    name: 'Traditional Wear',
    description: 'Cultural and traditional dress styles',
    image: 'traditional-preview.jpg',
    available: false,
    comingSoon: true,
  },
];
