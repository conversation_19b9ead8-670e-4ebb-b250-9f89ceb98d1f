import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { 
  validatePassword, 
  validateLoginPassword,
  PasswordValidationResult,
  PasswordValidationOptions,
  getPasswordStrengthColor,
  getPasswordStrengthMessage,
  createDebouncedValidator
} from '../../utils/passwordValidation';
import { useResponsive } from '../../utils/responsive';

interface PasswordInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  validationType?: 'register' | 'login' | 'change';
  showStrengthIndicator?: boolean;
  showValidationErrors?: boolean;
  onValidationChange?: (result: PasswordValidationResult) => void;
  style?: any;
  validationOptions?: PasswordValidationOptions;
}

export const PasswordInput: React.FC<PasswordInputProps> = ({
  value,
  onChangeText,
  placeholder = 'Password',
  validationType = 'register',
  showStrengthIndicator = true,
  showValidationErrors = true,
  onValidationChange,
  style,
  validationOptions
}) => {
  const [isSecure, setIsSecure] = useState(true);
  const [validationResult, setValidationResult] = useState<PasswordValidationResult | null>(null);
  const [showErrors, setShowErrors] = useState(false);
  const { fontSize, spacing, getResponsiveStyles } = useResponsive();
  
  const strengthAnimation = new Animated.Value(0);

  // Create debounced validator
  const debouncedValidator = createDebouncedValidator((result) => {
    setValidationResult(result);
    if (onValidationChange) {
      onValidationChange(result);
    }
    
    // Animate strength indicator
    Animated.timing(strengthAnimation, {
      toValue: result.score,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, 300);

  // Validate password when value changes
  useEffect(() => {
    if (value.length > 0) {
      if (validationType === 'login') {
        const result = validateLoginPassword(value);
        setValidationResult(result);
        if (onValidationChange) {
          onValidationChange(result);
        }
      } else {
        debouncedValidator(value, validationOptions);
      }
      setShowErrors(true);
    } else {
      setValidationResult(null);
      setShowErrors(false);
      if (onValidationChange) {
        onValidationChange({ isValid: false, errors: [], strength: 'weak', score: 0 });
      }
    }
  }, [value, validationType, validationOptions]);

  const getBorderColor = () => {
    if (!validationResult || value.length === 0) {
      return '#e5e7eb';
    }
    
    if (validationType === 'login') {
      return value.length >= 1 ? '#10b981' : '#ef4444';
    }
    
    return validationResult.isValid ? '#10b981' : '#ef4444';
  };

  const getStrengthBarWidth = () => {
    return strengthAnimation.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
      extrapolate: 'clamp',
    });
  };

  return (
    <View style={[styles.container, style]}>
      {/* Password Input */}
      <View style={[
        styles.inputContainer,
        { borderColor: getBorderColor() },
        getResponsiveStyles({
          padding: spacing(12),
          borderRadius: spacing(8),
        })
      ]}>
        <TextInput
          style={[
            styles.input,
            { fontSize: fontSize(16) }
          ]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor="#9ca3af"
          secureTextEntry={isSecure}
          autoCapitalize="none"
          autoCorrect={false}
        />
        <TouchableOpacity
          style={styles.eyeButton}
          onPress={() => setIsSecure(!isSecure)}
        >
          <Ionicons
            name={isSecure ? 'eye-off' : 'eye'}
            size={20}
            color="#6b7280"
          />
        </TouchableOpacity>
      </View>

      {/* Strength Indicator */}
      {showStrengthIndicator && validationType !== 'login' && validationResult && value.length > 0 && (
        <View style={[styles.strengthContainer, { marginTop: spacing(8) }]}>
          <View style={styles.strengthBar}>
            <Animated.View
              style={[
                styles.strengthFill,
                {
                  width: getStrengthBarWidth(),
                  backgroundColor: getPasswordStrengthColor(validationResult.strength),
                }
              ]}
            />
          </View>
          <Text style={[
            styles.strengthText,
            { 
              fontSize: fontSize(12),
              color: getPasswordStrengthColor(validationResult.strength)
            }
          ]}>
            {getPasswordStrengthMessage(validationResult.strength)}
          </Text>
        </View>
      )}

      {/* Validation Errors */}
      {showValidationErrors && showErrors && validationResult && validationResult.errors.length > 0 && (
        <View style={[styles.errorsContainer, { marginTop: spacing(8) }]}>
          {validationResult.errors.map((error, index) => (
            <Text
              key={index}
              style={[
                styles.errorText,
                { fontSize: fontSize(12) }
              ]}
            >
              {error}
            </Text>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    backgroundColor: '#f9fafb',
  },
  input: {
    flex: 1,
    color: '#1f2937',
    fontWeight: '500',
  },
  eyeButton: {
    padding: 4,
  },
  strengthContainer: {
    width: '100%',
  },
  strengthBar: {
    height: 4,
    backgroundColor: '#e5e7eb',
    borderRadius: 2,
    overflow: 'hidden',
  },
  strengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  strengthText: {
    marginTop: 4,
    fontWeight: '500',
    textAlign: 'center',
  },
  errorsContainer: {
    width: '100%',
  },
  errorText: {
    color: '#ef4444',
    marginBottom: 2,
    fontWeight: '500',
  },
});
