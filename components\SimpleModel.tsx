import { useFrame } from '@react-three/fiber';
import React, { useRef } from 'react';

// Simple 3D human-like figure with gesture controls
export function SimpleModel({ rotationX = 0, rotationY = 0, isManualRotation = false }: {
  rotationX?: number,
  rotationY?: number,
  isManualRotation?: boolean
}) {
  const groupRef = useRef<any>(null);
  const autoRotationRef = useRef(0);

  useFrame((state, delta) => {
    if (groupRef.current) {
      // Only manual rotation - no auto-rotation
      groupRef.current.rotation.y = rotationY;
      groupRef.current.rotation.x = rotationX;

      // Add subtle breathing animation
      groupRef.current.scale.setScalar(1.6 + Math.sin(state.clock.elapsedTime * 2) * 0.05);
    }
  });
  
  return (
    <group ref={groupRef} scale={[1.6, 1.6, 1.6]} position={[0, -1.5, 0]}>
      {/* Body */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[0.8, 1.2, 0.4]} />
        <meshLambertMaterial color="#4169e1" emissive="#001122" />
      </mesh>

      {/* Head */}
      <mesh position={[0, 1.2, 0]}>
        <sphereGeometry args={[0.35, 16, 16]} />
        <meshLambertMaterial color="#fdbcb4" emissive="#221100" />
      </mesh>
      
      {/* Arms */}
      <mesh position={[-0.6, 0.3, 0]}>
        <boxGeometry args={[0.2, 1, 0.2]} />
        <meshLambertMaterial color="#fdbcb4" />
      </mesh>
      <mesh position={[0.6, 0.3, 0]}>
        <boxGeometry args={[0.2, 1, 0.2]} />
        <meshLambertMaterial color="#fdbcb4" />
      </mesh>
      
      {/* Legs */}
      <mesh position={[-0.25, -1.2, 0]}>
        <boxGeometry args={[0.25, 1.2, 0.25]} />
        <meshLambertMaterial color="#2c3e50" />
      </mesh>
      <mesh position={[0.25, -1.2, 0]}>
        <boxGeometry args={[0.25, 1.2, 0.25]} />
        <meshLambertMaterial color="#2c3e50" />
      </mesh>
      
      {/* Hair */}
      <mesh position={[0, 1.4, 0]}>
        <boxGeometry args={[0.6, 0.3, 0.6]} />
        <meshLambertMaterial color="#8b4513" />
      </mesh>
    </group>
  );
}

// Alternative: Load GLB but with aggressive texture stripping
export function SafeGLBModel({ modelPath }: { modelPath: string }) {
  const groupRef = useRef<any>(null);
  
  // This will be implemented if the simple model works
  return <SimpleModel />;
}

export default SimpleModel;
