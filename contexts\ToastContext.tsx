import React, { createContext, ReactNode, useContext, useState } from 'react';
import { ToastContainer, ToastData } from '../components/Toast/ToastContainer';

interface ToastContextType {
  showToast: (toast: Omit<ToastData, 'id'>) => string;
  dismissToast: (id: string) => void;
  dismissAll: () => void;
  success: (title: string, message?: string, options?: Partial<ToastData>) => string;
  error: (title: string, message?: string, options?: Partial<ToastData>) => string;
  warning: (title: string, message?: string, options?: Partial<ToastData>) => string;
  info: (title: string, message?: string, options?: Partial<ToastData>) => string;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  const generateId = (): string => {
    return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const showToast = (toastData: Omit<ToastData, 'id'>): string => {
    const id = generateId();
    const newToast: ToastData = {
      id,
      duration: 6000, // Increased default duration for funny messages
      ...toastData,
    };

    setToasts(prev => [...prev, newToast]);
    return id;
  };

  const dismissToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const dismissAll = () => {
    setToasts([]);
  };

  const success = (title: string, message?: string, options?: Partial<ToastData>): string => {
    return showToast({
      type: 'success',
      title,
      message,
      ...options,
    });
  };

  const error = (title: string, message?: string, options?: Partial<ToastData>): string => {
    return showToast({
      type: 'error',
      title,
      message,
      duration: 8000, // Longer duration for funny error messages
      ...options,
    });
  };

  const warning = (title: string, message?: string, options?: Partial<ToastData>): string => {
    return showToast({
      type: 'warning',
      title,
      message,
      ...options,
    });
  };

  const info = (title: string, message?: string, options?: Partial<ToastData>): string => {
    return showToast({
      type: 'info',
      title,
      message,
      ...options,
    });
  };

  const value: ToastContextType = {
    showToast,
    dismissToast,
    dismissAll,
    success,
    error,
    warning,
    info,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onDismiss={dismissToast} />
    </ToastContext.Provider>
  );
};
