import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { PasswordInput } from '../common/PasswordInput';
import { PasswordValidationResult, validatePassword } from '../../utils/passwordValidation';

export const PasswordValidationTest: React.FC = () => {
  const [testPassword, setTestPassword] = useState('');
  const [validationResult, setValidationResult] = useState<PasswordValidationResult | null>(null);

  const handleValidationChange = (result: PasswordValidationResult) => {
    setValidationResult(result);
  };

  const testPasswords = [
    'weak',
    'password123',
    'StrongPass123!',
    'MySecureP@ssw0rd',
    'a',
    'verylongpasswordwithoutspecialcharacters',
  ];

  const testPassword_ = (password: string) => {
    setTestPassword(password);
    const result = validatePassword(password);
    setValidationResult(result);
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🔒 Password Validation Test</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Interactive Test:</Text>
        <PasswordInput
          value={testPassword}
          onChangeText={setTestPassword}
          placeholder="Type a password to test..."
          validationType="register"
          showStrengthIndicator={true}
          showValidationErrors={true}
          onValidationChange={handleValidationChange}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Tests:</Text>
        {testPasswords.map((password, index) => (
          <TouchableOpacity
            key={index}
            style={styles.testButton}
            onPress={() => testPassword_(password)}
          >
            <Text style={styles.testButtonText}>Test: "{password}"</Text>
          </TouchableOpacity>
        ))}
      </View>

      {validationResult && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Validation Result:</Text>
          <View style={styles.resultContainer}>
            <Text style={[styles.resultText, { color: validationResult.isValid ? '#10b981' : '#ef4444' }]}>
              Valid: {validationResult.isValid ? '✅ YES' : '❌ NO'}
            </Text>
            <Text style={styles.resultText}>
              Strength: {validationResult.strength.toUpperCase()} ({validationResult.score}/100)
            </Text>
            {validationResult.errors.length > 0 && (
              <View style={styles.errorsContainer}>
                <Text style={styles.errorsTitle}>Errors:</Text>
                {validationResult.errors.map((error, index) => (
                  <Text key={index} style={styles.errorText}>• {error}</Text>
                ))}
              </View>
            )}
          </View>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f9fafb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
    color: '#1f2937',
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#374151',
  },
  testButton: {
    backgroundColor: '#3b82f6',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  testButtonText: {
    color: '#fff',
    fontWeight: '500',
    textAlign: 'center',
  },
  resultContainer: {
    backgroundColor: '#f3f4f6',
    padding: 12,
    borderRadius: 8,
  },
  resultText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  errorsContainer: {
    marginTop: 8,
  },
  errorsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ef4444',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#ef4444',
    marginBottom: 2,
  },
});
