import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { apiService } from '../../services/api';

interface FaceMeasurement {
  analysisId: string;
  faceWidth: number;
  faceHeight: number;
  lengthToWidthRatio: number;
  confidence: number;
  createdAt: string;
  fileName?: string;
  imageUrl?: string;
  measurements?: any; // Detailed measurements from API
}

export const MeasurementsHistory: React.FC = () => {
  const [measurements, setMeasurements] = useState<FaceMeasurement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [expandedId, setExpandedId] = useState<string | null>(null);

  useEffect(() => {
    loadMeasurementsHistory();
  }, []);

  const loadMeasurementsHistory = async () => {
    try {
      setIsLoading(true);
      console.log('📏 Loading measurements history...');

      // Get the analysis history with new API format
      const response = await apiService.getFaceAnalysisHistory();
      console.log('📏 Analysis history response:', response);

      if (response.success && response.data?.analyses) {
        // Transform backend response to measurements format with detailed data
        const measurementsList: FaceMeasurement[] = [];

        for (const analysis of response.data.analyses) {
          try {
            // Try to get detailed measurements for each analysis
            const detailedMeasurements = await apiService.getDetailedFaceMeasurements(analysis._id);

            if (detailedMeasurements.success && detailedMeasurements.data) {
              measurementsList.push({
                analysisId: analysis._id,
                faceWidth: detailedMeasurements.data.faceDimensions?.faceWidth || analysis.faceDimensions?.faceWidth || 0,
                faceHeight: detailedMeasurements.data.faceDimensions?.faceHeight || analysis.faceDimensions?.faceHeight || 0,
                lengthToWidthRatio: detailedMeasurements.data.faceDimensions?.lengthToWidthRatio || analysis.faceDimensions?.lengthToWidthRatio || 0,
                confidence: detailedMeasurements.data.measurements?.confidence || analysis.analysisMetadata?.confidence || 0.85,
                createdAt: analysis.createdAt,
                fileName: analysis.originalFileName || 'Unknown file',
                imageUrl: analysis.imageUrl,
                // Add detailed measurements data
                measurements: detailedMeasurements.data
              });
            } else {
              // Fallback to basic measurements from analysis
              if (analysis.faceDimensions) {
                measurementsList.push({
                  analysisId: analysis._id,
                  faceWidth: analysis.faceDimensions?.faceWidth || 0,
                  faceHeight: analysis.faceDimensions?.faceHeight || 0,
                  lengthToWidthRatio: analysis.faceDimensions?.lengthToWidthRatio || 0,
                  confidence: analysis.analysisMetadata?.confidence || 0.85,
                  createdAt: analysis.createdAt,
                  fileName: analysis.originalFileName || 'Unknown file',
                  imageUrl: analysis.imageUrl
                });
              }
            }
          } catch (measurementError) {
            console.log('📏 Failed to get detailed measurements for analysis:', analysis._id, measurementError);
            // Continue with basic measurements if detailed ones fail
            if (analysis.faceDimensions) {
              measurementsList.push({
                analysisId: analysis._id,
                faceWidth: analysis.faceDimensions?.faceWidth || 0,
                faceHeight: analysis.faceDimensions?.faceHeight || 0,
                lengthToWidthRatio: analysis.faceDimensions?.lengthToWidthRatio || 0,
                confidence: analysis.analysisMetadata?.confidence || 0.85,
                createdAt: analysis.createdAt,
                fileName: analysis.originalFileName || 'Unknown file',
                imageUrl: analysis.imageUrl
              });
            }
          }
        }

        console.log('📏 Transformed measurements:', measurementsList.length);
        setMeasurements(measurementsList);
      } else {
        console.log('📏 No measurements available');
        setMeasurements([]);
      }
    } catch (error: any) {
      console.error('❌ Failed to load measurements history:', error);
      Alert.alert('Error', 'Failed to load measurements history: ' + error.message);
      setMeasurements([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadMeasurementsHistory();
    setIsRefreshing(false);
  };

  const toggleExpanded = (analysisId: string) => {
    setExpandedId(expandedId === analysisId ? null : analysisId);
  };

  const renderMeasurementValue = (label: string, value: any, unit: string = '') => {
    if (value === null || value === undefined) return null;
    
    return (
      <View style={styles.measurementRow}>
        <Text style={styles.measurementLabel}>{label}:</Text>
        <Text style={styles.measurementValue}>
          {typeof value === 'number' ? value.toFixed(2) : value}{unit}
        </Text>
      </View>
    );
  };

  const renderMeasurementItem = (measurement: FaceMeasurement, index: number) => {
    const isExpanded = expandedId === measurement.analysisId;
    const hasMeasurements = measurement.measurements && typeof measurement.measurements === 'object';
    
    return (
      <View key={measurement.analysisId} style={styles.measurementCard}>
        <TouchableOpacity
          style={styles.measurementHeader}
          onPress={() => toggleExpanded(measurement.analysisId)}
        >
          <View style={styles.measurementHeaderLeft}>
            <Ionicons name="resize" size={24} color="#667eea" />
            <View style={styles.measurementHeaderText}>
              <Text style={styles.measurementTitle}>
                Measurements #{index + 1}
              </Text>
              <Text style={styles.measurementSubtitle}>
                {measurement.fileName || 'Unknown file'}
              </Text>
              <Text style={styles.measurementStatus}>
                {hasMeasurements ? 'Available' : 'Not available'}
              </Text>
            </View>
          </View>
          <Ionicons 
            name={isExpanded ? "chevron-up" : "chevron-down"} 
            size={20} 
            color="#999" 
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.measurementDetails}>
            {hasMeasurements ? (
              <View style={styles.measurementsContainer}>
                {/* Face Dimensions */}
                {measurement.measurements.dimensions && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Face Dimensions</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Face Length', measurement.measurements.dimensions.faceLength, 'px')}
                      {renderMeasurementValue('Face Width', measurement.measurements.dimensions.faceWidth, 'px')}
                      {renderMeasurementValue('Jaw Width', measurement.measurements.dimensions.jawWidth, 'px')}
                      {renderMeasurementValue('Forehead Width', measurement.measurements.dimensions.foreheadWidth, 'px')}
                      {renderMeasurementValue('Cheekbone Width', measurement.measurements.dimensions.cheekboneWidth, 'px')}
                    </View>
                  </View>
                )}

                {/* Ratios */}
                {measurement.measurements.dimensions && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Face Ratios</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Length to Width', measurement.measurements.dimensions.lengthToWidthRatio)}
                      {renderMeasurementValue('Jaw to Forehead', measurement.measurements.dimensions.jawToForeheadRatio)}
                      {renderMeasurementValue('Cheekbone to Jaw', measurement.measurements.dimensions.cheekboneToJawRatio)}
                    </View>
                  </View>
                )}

                {/* Basic Measurements */}
                <View style={styles.measurementSection}>
                  <Text style={styles.measurementSectionTitle}>Basic Measurements</Text>
                  <View style={styles.measurementGrid}>
                    {renderMeasurementValue('Face Width', measurement.faceWidth, 'px')}
                    {renderMeasurementValue('Face Height', measurement.faceHeight, 'px')}
                    {renderMeasurementValue('Length/Width Ratio', measurement.lengthToWidthRatio)}
                    {renderMeasurementValue('Confidence', Math.round(measurement.confidence * 100), '%')}
                  </View>
                </View>

                {/* Detailed Face Dimensions */}
                {measurement.measurements?.faceDimensions && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Detailed Face Dimensions</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Jawline Width', measurement.measurements.faceDimensions.jawlineWidth, 'px')}
                      {renderMeasurementValue('Forehead Width', measurement.measurements.faceDimensions.foreheadWidth, 'px')}
                      {renderMeasurementValue('Cheekbone Width', measurement.measurements.faceDimensions.cheekboneWidth, 'px')}
                      {renderMeasurementValue('Chin Width', measurement.measurements.faceDimensions.chinWidth, 'px')}
                    </View>
                  </View>
                )}

                {/* Facial Features */}
                {measurement.measurements?.facialFeatures && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Facial Features</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Face Shape', measurement.measurements.facialFeatures.faceShape)}
                      {renderMeasurementValue('Eye Shape', measurement.measurements.facialFeatures.eyeFeatures?.eyeShape)}
                      {renderMeasurementValue('Nose Shape', measurement.measurements.facialFeatures.noseFeatures?.noseShape)}
                      {renderMeasurementValue('Lip Shape', measurement.measurements.facialFeatures.lipFeatures?.lipShape)}
                    </View>
                  </View>
                )}

                {/* Eye Features */}
                {measurement.measurements?.facialFeatures?.eyeFeatures && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Eye Measurements</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Left Eye Width', measurement.measurements.facialFeatures.eyeFeatures.leftEyeWidth, 'px')}
                      {renderMeasurementValue('Right Eye Width', measurement.measurements.facialFeatures.eyeFeatures.rightEyeWidth, 'px')}
                      {renderMeasurementValue('Eye Spacing', measurement.measurements.facialFeatures.eyeFeatures.eyeSpacing, 'px')}
                      {renderMeasurementValue('Eye Symmetry', measurement.measurements.facialFeatures.eyeFeatures.eyeSymmetry)}
                    </View>
                  </View>
                )}

                {/* Proportions */}
                {measurement.measurements?.proportions && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Face Proportions</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Golden Ratio', measurement.measurements.proportions.goldenRatio)}
                      {renderMeasurementValue('Proportion Score', measurement.measurements.proportions.faceProportionScore)}
                      {renderMeasurementValue('Symmetry Score', measurement.measurements.proportions.symmetryScore)}
                      {renderMeasurementValue('Harmonic Mean', measurement.measurements.proportions.harmonicMean)}
                    </View>
                  </View>
                )}

                {/* Legacy Eye Distance */}
                {measurement.measurements?.eyeDistance && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Legacy Eye Measurements</Text>
                    <View style={styles.measurementGrid}>
                      {renderMeasurementValue('Eye Distance', measurement.measurements.eyeDistance, 'px')}
                    </View>
                  </View>
                )}

                {/* Raw Data (if available) */}
                {Object.keys(measurement.measurements).length > 0 && (
                  <View style={styles.measurementSection}>
                    <Text style={styles.measurementSectionTitle}>Additional Data</Text>
                    <View style={styles.rawDataContainer}>
                      <Text style={styles.rawDataText}>
                        {JSON.stringify(measurement.measurements, null, 2)}
                      </Text>
                    </View>
                  </View>
                )}
              </View>
            ) : (
              <View style={styles.noMeasurementsContainer}>
                <Ionicons name="resize-outline" size={30} color="#bdc3c7" />
                <Text style={styles.noMeasurementsText}>
                  Measurements not available for this analysis
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>Loading measurements history...</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container} 
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Ionicons name="resize" size={40} color="#fff" />
          <Text style={styles.headerTitle}>Measurements History</Text>
          <Text style={styles.headerSubtitle}>
            {measurements.length} measurement{measurements.length !== 1 ? 's' : ''} found
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        {measurements.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="resize-outline" size={60} color="#bdc3c7" />
            <Text style={styles.emptyStateTitle}>No Measurements History</Text>
            <Text style={styles.emptyStateText}>
              You haven't generated any face measurements yet. Perform a face analysis to get detailed facial measurements!
            </Text>
          </View>
        ) : (
          measurements.map((measurement, index) => renderMeasurementItem(measurement, index))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#7f8c8d',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 10,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginTop: 5,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  measurementCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  measurementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  measurementHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  measurementHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  measurementTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 2,
  },
  measurementSubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  measurementStatus: {
    fontSize: 12,
    color: '#95a5a6',
  },
  measurementDetails: {
    borderTopWidth: 1,
    borderTopColor: '#f1f2f6',
    padding: 16,
  },
  measurementsContainer: {
    gap: 20,
  },
  measurementSection: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 15,
  },
  measurementSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 12,
  },
  measurementGrid: {
    gap: 8,
  },
  measurementRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  measurementLabel: {
    fontSize: 14,
    color: '#7f8c8d',
    flex: 1,
  },
  measurementValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '600',
    fontFamily: 'monospace',
  },
  rawDataContainer: {
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e1e8ed',
  },
  rawDataText: {
    fontSize: 12,
    color: '#34495e',
    fontFamily: 'monospace',
  },
  noMeasurementsContainer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  noMeasurementsText: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 8,
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2c3e50',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 24,
  },
});
