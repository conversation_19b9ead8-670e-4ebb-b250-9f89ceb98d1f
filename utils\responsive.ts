import { Dimensions, PixelRatio } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Device types
export const DeviceTypes = {
  PHONE_SMALL: 'phone_small',    // < 375px width
  PHONE_MEDIUM: 'phone_medium',  // 375px - 414px width
  PHONE_LARGE: 'phone_large',    // 414px - 480px width
  TABLET_SMALL: 'tablet_small',  // 480px - 768px width
  TABLET_LARGE: 'tablet_large',  // > 768px width
} as const;

export type DeviceType = typeof DeviceTypes[keyof typeof DeviceTypes];

// Breakpoints
export const Breakpoints = {
  xs: 0,
  sm: 375,
  md: 414,
  lg: 480,
  xl: 768,
  xxl: 1024,
} as const;

class ResponsiveService {
  private screenWidth: number;
  private screenHeight: number;
  private pixelRatio: number;

  constructor() {
    this.screenWidth = SCREEN_WIDTH;
    this.screenHeight = SCREEN_HEIGHT;
    this.pixelRatio = PixelRatio.get();
  }

  // Get current device type
  getDeviceType(): DeviceType {
    if (this.screenWidth < Breakpoints.sm) return DeviceTypes.PHONE_SMALL;
    if (this.screenWidth < Breakpoints.md) return DeviceTypes.PHONE_MEDIUM;
    if (this.screenWidth < Breakpoints.lg) return DeviceTypes.PHONE_LARGE;
    if (this.screenWidth < Breakpoints.xl) return DeviceTypes.TABLET_SMALL;
    return DeviceTypes.TABLET_LARGE;
  }

  // Check if device is phone
  isPhone(): boolean {
    return this.screenWidth < Breakpoints.lg;
  }

  // Check if device is tablet
  isTablet(): boolean {
    return this.screenWidth >= Breakpoints.lg;
  }

  // Check if device is small phone
  isSmallPhone(): boolean {
    return this.screenWidth < Breakpoints.sm;
  }

  // Check if device is large phone
  isLargePhone(): boolean {
    return this.screenWidth >= Breakpoints.md && this.screenWidth < Breakpoints.lg;
  }

  // Responsive width based on percentage
  wp(percentage: number): number {
    return (this.screenWidth * percentage) / 100;
  }

  // Responsive height based on percentage
  hp(percentage: number): number {
    return (this.screenHeight * percentage) / 100;
  }

  // Responsive font size
  fontSize(size: number): number {
    const deviceType = this.getDeviceType();
    
    switch (deviceType) {
      case DeviceTypes.PHONE_SMALL:
        return size * 0.85;
      case DeviceTypes.PHONE_MEDIUM:
        return size * 0.9;
      case DeviceTypes.PHONE_LARGE:
        return size;
      case DeviceTypes.TABLET_SMALL:
        return size * 1.1;
      case DeviceTypes.TABLET_LARGE:
        return size * 1.2;
      default:
        return size;
    }
  }

  // Responsive spacing
  spacing(size: number): number {
    const deviceType = this.getDeviceType();
    
    switch (deviceType) {
      case DeviceTypes.PHONE_SMALL:
        return size * 0.8;
      case DeviceTypes.PHONE_MEDIUM:
        return size * 0.9;
      case DeviceTypes.PHONE_LARGE:
        return size;
      case DeviceTypes.TABLET_SMALL:
        return size * 1.2;
      case DeviceTypes.TABLET_LARGE:
        return size * 1.4;
      default:
        return size;
    }
  }

  // Responsive icon size
  iconSize(size: number): number {
    const deviceType = this.getDeviceType();
    
    switch (deviceType) {
      case DeviceTypes.PHONE_SMALL:
        return size * 0.85;
      case DeviceTypes.PHONE_MEDIUM:
        return size * 0.9;
      case DeviceTypes.PHONE_LARGE:
        return size;
      case DeviceTypes.TABLET_SMALL:
        return size * 1.15;
      case DeviceTypes.TABLET_LARGE:
        return size * 1.3;
      default:
        return size;
    }
  }

  // Get responsive styles based on device type
  getResponsiveStyles() {
    const deviceType = this.getDeviceType();
    
    return {
      // Container styles
      container: {
        paddingHorizontal: this.spacing(16),
        paddingVertical: this.spacing(12),
      },
      
      // Header styles
      header: {
        height: this.isTablet() ? 80 : 60,
        paddingHorizontal: this.spacing(16),
      },
      
      // Button styles
      button: {
        height: this.spacing(48),
        paddingHorizontal: this.spacing(24),
        borderRadius: this.spacing(12),
      },
      
      // Input styles
      input: {
        height: this.spacing(48),
        paddingHorizontal: this.spacing(16),
        fontSize: this.fontSize(16),
        borderRadius: this.spacing(8),
      },
      
      // Card styles
      card: {
        padding: this.spacing(16),
        borderRadius: this.spacing(12),
        marginBottom: this.spacing(12),
      },
      
      // Modal styles
      modal: {
        maxWidth: this.isTablet() ? this.wp(60) : this.wp(90),
        padding: this.spacing(24),
        borderRadius: this.spacing(16),
      },
      
      // Typography
      typography: {
        h1: this.fontSize(32),
        h2: this.fontSize(28),
        h3: this.fontSize(24),
        h4: this.fontSize(20),
        h5: this.fontSize(18),
        h6: this.fontSize(16),
        body1: this.fontSize(16),
        body2: this.fontSize(14),
        caption: this.fontSize(12),
      },
      
      // Grid system
      grid: {
        columns: this.isTablet() ? 3 : 2,
        gap: this.spacing(12),
      },
      
      // 3D Model container
      modelContainer: {
        height: this.isTablet() ? this.hp(60) : this.hp(50),
        width: '100%',
      },
      
      // Color palette
      colorPalette: {
        itemSize: this.isTablet() ? 80 : 60,
        gap: this.spacing(8),
      },
    };
  }

  // Get device info
  getDeviceInfo() {
    return {
      width: this.screenWidth,
      height: this.screenHeight,
      pixelRatio: this.pixelRatio,
      deviceType: this.getDeviceType(),
      isPhone: this.isPhone(),
      isTablet: this.isTablet(),
      isSmallPhone: this.isSmallPhone(),
      isLargePhone: this.isLargePhone(),
    };
  }

  // Update dimensions (call this on orientation change)
  updateDimensions() {
    const { width, height } = Dimensions.get('window');
    this.screenWidth = width;
    this.screenHeight = height;
  }
}

export const responsiveService = new ResponsiveService();

// Convenience functions
export const wp = (percentage: number) => responsiveService.wp(percentage);
export const hp = (percentage: number) => responsiveService.hp(percentage);
export const fontSize = (size: number) => responsiveService.fontSize(size);
export const spacing = (size: number) => responsiveService.spacing(size);
export const iconSize = (size: number) => responsiveService.iconSize(size);
export const getResponsiveStyles = () => responsiveService.getResponsiveStyles();
export const getDeviceInfo = () => responsiveService.getDeviceInfo();

// Responsive breakpoint hooks
export const useResponsive = () => {
  return {
    wp,
    hp,
    fontSize,
    spacing,
    iconSize,
    getResponsiveStyles,
    getDeviceInfo,
    isPhone: responsiveService.isPhone(),
    isTablet: responsiveService.isTablet(),
    isSmallPhone: responsiveService.isSmallPhone(),
    isLargePhone: responsiveService.isLargePhone(),
    deviceType: responsiveService.getDeviceType(),
  };
};
