import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { ColorCombination, ColorService } from '../services/colorService';

export interface UserColors {
  hairColor: { primary: string; hex: string; rgb: { r: number; g: number; b: number } };
  skinTone: { primary: string; hex: string; rgb: { r: number; g: number; b: number } };
  eyeColor: { primary: string; hex: string; rgb: { r: number; g: number; b: number } };
  lipColor: { primary: string; hex: string; rgb: { r: number; g: number; b: number } };
}

interface ColorContextType {
  currentCombinations: ColorCombination[];
  selectedCombination: number;
  userColors: UserColors | null;
  isLoadingCombinations: boolean;
  colorSource: 'api' | 'default' | 'loading'; // Track color source
  colorSourceMessage: string; // User-friendly message about color source
  setColorCombinations: (combinations: ColorCombination[]) => void;
  setSelectedCombination: (index: number) => void;
  setUserColors: (colors: UserColors | null) => void;
  getCurrentCombination: () => ColorCombination | null;
  applyColorsToModel: (materials: any, gender: 'male' | 'female') => void;
  loadDefaultCombinations: (faceColors: UserColors) => Promise<void>;
  loadUserRecommendations: () => Promise<boolean>;
  getCurrentModelColors: () => {
    hairColor: string;
    shirtColor: string;
    pantsColor: string;
    shoesColor: string;
  };
}

const ColorContext = createContext<ColorContextType | undefined>(undefined);

export const useColor = () => {
  const context = useContext(ColorContext);
  if (context === undefined) {
    throw new Error('useColor must be used within a ColorProvider');
  }
  return context;
};

interface ColorProviderProps {
  children: ReactNode;
}

export const ColorProvider: React.FC<ColorProviderProps> = ({ children }) => {
  const [currentCombinations, setCurrentCombinations] = useState<ColorCombination[]>([]);
  const [selectedCombination, setSelectedCombination] = useState<number>(0);
  const [userColors, setUserColorsState] = useState<UserColors | null>(null);
  const [isLoadingCombinations, setIsLoadingCombinations] = useState(false);
  const [colorSource, setColorSource] = useState<'api' | 'default' | 'loading'>('loading');
  const [colorSourceMessage, setColorSourceMessage] = useState<string>('Loading color recommendations...');

  const setColorCombinations = (combinations: ColorCombination[]) => {
    console.log('🎨 ColorContext: Setting color combinations from current analysis:', combinations.length);
    setCurrentCombinations(combinations);
    setSelectedCombination(0); // Reset to first combination

    // Update source to indicate these are from current analysis
    if (combinations.length > 0) {
      setColorSource('api');
      setColorSourceMessage('✨ Colors from your current face analysis');
    }
  };

  const setUserColors = (colors: UserColors | null) => {
    console.log('ColorContext: Setting user colors:', colors);
    setUserColorsState(colors);
  };

  const getCurrentCombination = (): ColorCombination | null => {
    if (currentCombinations.length > 0 && selectedCombination < currentCombinations.length) {
      return currentCombinations[selectedCombination];
    }
    return null;
  };

  const loadDefaultCombinations = async (faceColors: UserColors) => {
    try {
      setIsLoadingCombinations(true);
      setColorSource('loading');
      setColorSourceMessage('Loading color combinations...');

      console.log('ColorContext: Loading default color combinations (Gemini API removed)');

      // Use default combinations with female-friendly colors
      const defaultCombinations: ColorCombination[] = [
        { name: 'Elegant Pink', shirt: '#e91e63', pants: '#8e24aa', shoes: '#000000' },
        { name: 'Royal Blue', shirt: '#1e3a8a', pants: '#374151', shoes: '#000000' },
        { name: 'Spring Fresh', shirt: '#4caf50', pants: '#2e7d32', shoes: '#8b4513' },
        { name: 'Sunset Orange', shirt: '#ff9800', pants: '#f57c00', shoes: '#654321' },
        { name: 'Classic White', shirt: '#ffffff', pants: '#9c27b0', shoes: '#000000' },
        { name: 'Modern Purple', shirt: '#9c27b0', pants: '#673ab7', shoes: '#374151' },
      ];

      setCurrentCombinations(defaultCombinations);
      setSelectedCombination(0);

      // Set default source
      setColorSource('default');
      setColorSourceMessage('🎨 Default Color Combinations');
      console.log('🎨 ColorContext: ❌ SHOWING DEFAULT COLORS (from loadDefaultCombinations)');
      console.log('ColorContext: Loaded', defaultCombinations.length, 'default combinations');
    } catch (error) {
      console.error('ColorContext: Failed to load combinations:', error);
      // Set minimal default combinations on error
      const defaultCombinations: ColorCombination[] = [
        { name: 'Default', shirt: '#ffffff', pants: '#1e40af', shoes: '#000000' },
      ];
      setCurrentCombinations(defaultCombinations);
      setSelectedCombination(0);

      // Set default source even on error
      setColorSource('default');
      setColorSourceMessage('🎨 Default Color Combinations');
    } finally {
      setIsLoadingCombinations(false);
    }
  };

  // Load user recommendations automatically
  const loadUserRecommendations = async () => {
    setIsLoadingCombinations(true);
    setColorSource('loading');
    setColorSourceMessage('Loading your color recommendations...');

    try {
      console.log('🎨 ColorContext: Loading user recommendations...');

      // Import the services dynamically to avoid circular dependencies
      const { userRecommendationService } = await import('../services/userRecommendationService');
      const { createMultipleColorCombinationsFromRecommendation } = await import('../utils/recommendationToColorCombination');

      const recentRecommendation = await userRecommendationService.getMostRecentRecommendation();

      if (recentRecommendation.hasRecommendations && recentRecommendation.recommendation) {
        console.log('🎨 ColorContext: Found user recommendations, creating color combinations...');

        const recommendedCombinations = createMultipleColorCombinationsFromRecommendation(recentRecommendation);

        if (recommendedCombinations.length > 0) {
          console.log('🎨 ColorContext: Setting recommended combinations:', recommendedCombinations.length);
          setCurrentCombinations(recommendedCombinations);
          setSelectedCombination(0);

          // Set API source with personalized message
          setColorSource('api');
          setColorSourceMessage('✨ Your Recent Color Recommendations');
          console.log('🎨 ColorContext: ✅ SHOWING API RECOMMENDATIONS');
          return true; // Successfully loaded recommendations
        }
      }

      console.log('🎨 ColorContext: No user recommendations found, loading defaults...');

      // Fallback to default combinations
      const defaultCombinations: ColorCombination[] = [
        { name: 'Elegant Pink', shirt: '#e91e63', pants: '#8e24aa', shoes: '#000000' },
        { name: 'Royal Blue', shirt: '#1e3a8a', pants: '#374151', shoes: '#000000' },
        { name: 'Spring Fresh', shirt: '#4caf50', pants: '#2e7d32', shoes: '#8b4513' },
        { name: 'Sunset Orange', shirt: '#ff9800', pants: '#f57c00', shoes: '#654321' },
        { name: 'Classic White', shirt: '#ffffff', pants: '#9c27b0', shoes: '#000000' },
        { name: 'Modern Purple', shirt: '#9c27b0', pants: '#673ab7', shoes: '#374151' },
      ];

      setCurrentCombinations(defaultCombinations);
      setSelectedCombination(0);

      // Set default source with clear message
      setColorSource('default');
      setColorSourceMessage('🎨 Default Color Combinations');
      console.log('🎨 ColorContext: ❌ SHOWING DEFAULT COLORS');
      return false; // No recommendations found, used defaults
    } catch (error) {
      console.error('🎨 ColorContext: Error loading user recommendations:', error);

      // Emergency fallback
      const emergencyDefaults: ColorCombination[] = [
        { name: 'Default', shirt: '#e91e63', pants: '#8e24aa', shoes: '#000000' },
      ];
      setCurrentCombinations(emergencyDefaults);
      setSelectedCombination(0);

      // Set default source with error message
      setColorSource('default');
      setColorSourceMessage('🎨 Default Color Combinations');
      console.log('🎨 ColorContext: ❌ ERROR - SHOWING DEFAULT COLORS');
      return false;
    } finally {
      setIsLoadingCombinations(false);
    }
  };

  const getCurrentModelColors = () => {
    const combination = getCurrentCombination();
    return {
      hairColor: userColors?.hairColor?.hex || '#4a2c17',
      shirtColor: combination?.shirt || '#ffffff',
      pantsColor: combination?.pants || '#1e40af',
      shoesColor: combination?.shoes || '#000000',
    };
  };

  const applyColorsToModel = (materials: any, gender: 'male' | 'female') => {
    const combination = getCurrentCombination();
    if (!combination || !materials) return;

    // Apply user's hair color and combination colors
    const modelColors = getCurrentModelColors();

    if (gender === 'male') {
      ColorService.applyMaleModelColors(materials, combination, modelColors);
    } else {
      ColorService.applyFemaleModelColors(materials, combination, modelColors);
    }
  };

  // Refresh recommendations - but don't auto-load recent ones
  const refreshRecommendations = async () => {
    console.log('🎨 ColorContext: 🔄 Refresh called - but only showing current analysis colors...');
    // Don't load recent recommendations - only use current analysis colors
    console.log('🎨 ColorContext: Current combinations count:', currentCombinations.length);
  };

  // Don't auto-load recent recommendations on mount
  // Only show colors from current face analysis session
  useEffect(() => {
    console.log('🎨 ColorContext: Initialized - only showing current analysis colors (no recent recommendations)');
    // Set default state - no auto-loading of recent recommendations
    setColorSource('default');
    setColorSourceMessage('🎨 Upload your photo for personalized colors from current analysis');
  }, []); // Only run once on mount

  const value: ColorContextType = {
    currentCombinations,
    selectedCombination,
    userColors,
    isLoadingCombinations,
    colorSource,
    colorSourceMessage,
    setColorCombinations,
    setSelectedCombination,
    setUserColors,
    getCurrentCombination,
    applyColorsToModel,
    loadDefaultCombinations,
    loadUserRecommendations,
    getCurrentModelColors,
  };

  return (
    <ColorContext.Provider value={value}>
      {children}
    </ColorContext.Provider>
  );
};
