import { Ionicons } from '@expo/vector-icons';
import { Canvas, useFrame } from '@react-three/fiber';
import { Asset } from 'expo-asset';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Animated, Dimensions, PanResponder, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SvgXml } from 'react-native-svg';
import { CasualWearModel } from '../../components/CasualWearModel';
import { FemaleModel } from '../../components/FemaleModel';
import { FemaleModelSelector } from '../../components/FemaleModelSelector';
import { FormalDressModel } from '../../components/FormalDressModel';
import { useAuth } from '../../contexts/AuthContext';
import { ModelCategory } from '../../data/femaleModelCategories';

// Define color combination interface
interface ColorCombination {
  id: string;
  shirt: string;
  pants: string;
  shoes: string;
  description?: string;
  confidence?: number;
  source?: 'gemini' | 'default';
  name?: string;
}

const { width, height } = Dimensions.get('window');

// Calculate responsive scaling based on screen size
const getResponsiveScale = () => {
    const baseWidth = 375;
    const baseHeight = 667;
    const scaleX = width / baseWidth;
    const scaleY = height / baseHeight;
    const scale = Math.min(scaleX, scaleY);
    return Math.max(0.6, Math.min(1.4, scale));
};

// Unified model position for all models
const getModelPosition = (): [number, number, number] => {
    const isSmallScreen = width < 375 || height < 667;
    const isLargeScreen = width > 414 || height > 896;

    if (isSmallScreen) return [0, 3.0, 0];
    if (isLargeScreen) return [0, 4.0, 0];
    return [0, 3.5, 0];
};

// Unified scale for all models
const getModelScale = (): [number, number, number] => {
    const isSmallScreen = width < 375 || height < 667;
    const isLargeScreen = width > 414 || height > 896;

    let scale = 5;
    if (isSmallScreen) scale = 3;
    if (isLargeScreen) scale = 4;
    return [scale, scale, scale];
};

// Unified camera settings
const getCameraSettings = (): { position: [number, number, number], fov: number } => {
    const isSmallScreen = width < 375 || height < 667;
    const isLargeScreen = width > 414 || height > 896;

    let position: [number, number, number] = [0, 6.0, 12.5];
    let fov = 76;
    if (isSmallScreen) {
        position = [0, 5.0, 10.0];
        fov = 74;
    } else if (isLargeScreen) {
        position = [0, 7.0, 15.0];
        fov = 78;
    }
    return { position, fov };
};

// Optimized Female Model component
function FemaleModel3D({ rotationX, rotationY, isManualRotation, colorCombination, userColors, selectedCategory, zoom }: {
    rotationX: number,
    rotationY: number,
    isManualRotation: boolean,
    colorCombination?: any,
    userColors?: any,
    selectedCategory?: ModelCategory | null,
    zoom: number
}) {
    const [modelLoaded, setModelLoaded] = useState(false);
    const [useSimpleModel, setUseSimpleModel] = useState(false);
    const groupRef = useRef<any>(null);

    // Load female model
    useEffect(() => {
        const loadModel = async () => {
            try {
                const femaleAsset = Asset.fromModule(require('../../assets/models/female.glb'));
                await femaleAsset.downloadAsync();
                setModelLoaded(true);
            } catch (error) {
                console.log('Female model loading error:', error);
                setUseSimpleModel(true);
            }
        };
        loadModel();
    }, []);

    // HORIZONTAL ONLY rotation (Y-axis) - disable vertical rotation
    useFrame(() => {
        if (groupRef.current) {
            groupRef.current.rotation.x = 0; // Disable vertical tilt
            groupRef.current.rotation.y = rotationY; // Allow horizontal rotation
            groupRef.current.rotation.z = 0; // Disable roll rotation
        }
    });

    if (useSimpleModel) {
        return null;
    }

    if (!modelLoaded) {
        return null;
    }

    // Get unified scale and position
    const modelScale = getModelScale();
    const modelPosition = getModelPosition();

    // Apply zoom to model scale
    const zoomedScale: [number, number, number] = [
        modelScale[0] * zoom,
        modelScale[1] * zoom,
        modelScale[2] * zoom
    ];

    return (
        <group
            ref={groupRef}
            scale={zoomedScale} // Apply zoom to scale
            position={modelPosition}
        >
            {selectedCategory?.id === 'Women Formal Dress' ? (
                <FormalDressModel
                    dressColors={colorCombination}
                    modelScale={0.015 * zoom}
                    verticalOffset={-0.3}
                    enableColorSync={true}
                    displayMode="front"
                    rotationY={rotationY}
                    tiltX={0.2}
                />
            ) : selectedCategory?.id === 'casual' ? (
                <CasualWearModel
                    colorCombination={colorCombination}
                    rotationY={rotationY}
                    enableAutoRotation={false}
                    autoRotationSpeed={0.01}
                    manualRotationSensitivity={0.008}
                    position={[0, -1.0, 0]}
                    zoom={zoom}
                    isManualRotation={isManualRotation}
                />
            ) : (
                <FemaleModel
                    colorCombination={colorCombination}
                    userColors={userColors}
                    rotationX={rotationX}
                    rotationY={rotationY}
                    isManualRotation={isManualRotation}
                />
            )}
        </group>
    );
}

// Simple SVG Loading component
function SvgLoader() {
    const scaleValue = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        const pulseAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(scaleValue, {
                    toValue: 1.05,
                    duration: 800,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleValue, {
                    toValue: 0.95,
                    duration: 800,
                    useNativeDriver: true,
                }),
            ])
        );
        pulseAnimation.start();
        return () => pulseAnimation.stop();
    }, []);

    const svgContent = `<svg viewBox="0 0 200 300" fill="none" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <radialGradient id="femaleGrad" cx="50%" cy="50%" r="50%">
                <stop offset="0%" style="stop-color: #e91e63;"/>
                <stop offset="100%" style="stop-color: #f8bbd9;"/>
            </radialGradient>
        </defs>
        <circle cx="100" cy="60" r="30" fill="url(#femaleGrad)"/>
        <ellipse cx="100" cy="130" rx="35" ry="50" fill="url(#femaleGrad)"/>
        <ellipse cx="100" cy="220" rx="25" ry="60" fill="url(#femaleGrad)"/>
        <circle cx="90" cy="55" r="3" fill="#2c1810"/>
        <circle cx="110" cy="55" r="3" fill="#2c1810"/>
        <path d="M90,70 Q100,75 110,70" stroke="#d81b60" stroke-width="2" fill="none"/>
    </svg>`;

    return (
        <View style={styles.svgContainer}>
            <Animated.View style={[styles.svgWrapper, { transform: [{ scale: scaleValue }] }]}>
                <SvgXml xml={svgContent} width="150" height="225" />
            </Animated.View>
            <Text style={styles.loadingText}>Loading Model...</Text>
        </View>
    );
}

// Error boundary
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
    state = { hasError: false };

    static getDerivedStateFromError() {
        return { hasError: true };
    }

    render() {
        if (this.state.hasError) {
            return (
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>Model Loading Error</Text>
                </View>
            );
        }
        return this.props.children;
    }
}

// Main female model viewer
function FemaleModel3DViewer({ selectedCategory }: { selectedCategory?: ModelCategory | null }) {
    const { user } = useAuth();

    // State for color recommendations
    const [currentCombinations, setCurrentCombinations] = useState<ColorCombination[]>([]);
    const [selectedCombination, setSelectedCombination] = useState(0);
    const [userColors, setUserColors] = useState<any>(null);
    const [colorSource, setColorSource] = useState<'gemini' | 'loading' | 'none'>('loading');
    const [colorSourceMessage, setColorSourceMessage] = useState('Loading color recommendations...');
    const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);

    // Model state
    const [isManualRotation, setIsManualRotation] = useState(false);
    const [isModelLoaded, setIsModelLoaded] = useState(false);
    const [isModelReady, setIsModelReady] = useState(false);
    const [rotationX, setRotationX] = useState(0);
    const [rotationY, setRotationY] = useState(6.11); // Start with horizontal rotation 6.11
    const [zoom, setZoom] = useState(0.73); // Start at 0.73 zoom level

    const currentCombination = currentCombinations.length > 0 && selectedCombination < currentCombinations.length
        ? currentCombinations[selectedCombination]
        : null;

    // Load latest color recommendations from Gemini AI
    const loadCurrentColorRecommendations = async () => {
        setIsLoadingRecommendations(true);
        setColorSource('loading');
        setColorSourceMessage('Loading latest color recommendations...');

        try {
            console.log('🎨 Female Model: Loading latest color recommendations from new endpoint...');

            // Use the updated userRecommendationService to get latest recommendation
            const { userRecommendationService } = await import('../../services/userRecommendationService');
            const latestRecommendationData = await userRecommendationService.getLatestRecommendationData();

            if (latestRecommendationData && latestRecommendationData.recommendations && latestRecommendationData.recommendations.length > 0) {
                console.log('✅ Female Model: Using latest recommendations from new endpoint');

                // Convert recommendations to color combinations format
                const combinations = latestRecommendationData.recommendations.map((outfit: any, index: number) => ({
                    id: `latest-${index}`,
                    shirt: outfit.shirt?.hex || '#ffffff',
                    pants: outfit.pants?.hex || '#1e40af',
                    shoes: outfit.shoes?.hex || '#000000',
                    description: outfit.outfitName || `AI Outfit ${index + 1}`,
                    confidence: latestRecommendationData.confidence || 0.9,
                    source: 'gemini' as const,
                    name: outfit.outfitName || `Style ${index + 1}`,
                    reason: outfit.overallReason || 'AI recommended combination'
                }));

                setCurrentCombinations(combinations);
                setSelectedCombination(0);
                setColorSource('gemini');
                setColorSourceMessage('🤖 Latest AI Recommendations from New Endpoint');
                return;
            }

            // No recommendations available
            console.log('🎨 Female Model: No recommendations available from latest endpoint');
            setCurrentCombinations([]);
            setColorSource('none');
            setColorSourceMessage('No color recommendations available. Upload a photo for AI-powered color analysis.');

        } catch (error) {
            console.error('🎨 Female Model: Error loading recommendations from history:', error);
            setCurrentCombinations([]);
            setColorSource('none');
            setColorSourceMessage('Failed to load color recommendations. Please try again.');
        } finally {
            setIsLoadingRecommendations(false);
        }
    };

    // Load recommendations on component mount (always fresh data)
    useEffect(() => {
        console.log('🎨 Female Model: Loading fresh recommendations on mount');
        loadCurrentColorRecommendations();
    }, []);

    // Load fresh recommendations every time the page comes into focus
    const { useFocusEffect } = require('@react-navigation/native');
    useFocusEffect(
        React.useCallback(() => {
            console.log('🎨 Female Model: Page focused - clearing state and loading fresh recommendations');

            // Clear existing state to ensure fresh data
            setCurrentCombinations([]);
            setSelectedCombination(0);
            setColorSource('loading');
            setColorSourceMessage('Loading fresh color recommendations...');
            setIsLoadingRecommendations(true);

            // Load fresh recommendations
            loadCurrentColorRecommendations();
        }, [])
    );

    const lastTouchRef = useRef({ x: 0, y: 0 });
    const lastDistanceRef = useRef(0);
    const rotationSpeed = 0.01; // Increased sensitivity for smoother horizontal rotation
    const zoomSpeed = 0.002; // Zoom sensitivity

    const panResponder = PanResponder.create({
        onMoveShouldSetPanResponder: (_, gestureState) => {
            // Respond to horizontal movement (rotation) or multi-touch (zoom)
            return Math.abs(gestureState.dx) > 2 || gestureState.numberActiveTouches === 2;
        },
        onPanResponderGrant: (evt) => {
            setIsManualRotation(true);

            if (evt.nativeEvent.touches && evt.nativeEvent.touches.length === 2) {
                // Two finger touch - prepare for zoom
                const touch1 = evt.nativeEvent.touches[0];
                const touch2 = evt.nativeEvent.touches[1];
                const distance = Math.sqrt(
                    Math.pow(touch2.pageX - touch1.pageX, 2) +
                    Math.pow(touch2.pageY - touch1.pageY, 2)
                );
                lastDistanceRef.current = distance;
            } else {
                // Single finger touch - prepare for rotation
                lastTouchRef.current = {
                    x: evt.nativeEvent.pageX,
                    y: evt.nativeEvent.pageY,
                };
            }
        },
        onPanResponderMove: (evt) => {
            if (evt.nativeEvent.touches && evt.nativeEvent.touches.length === 2) {
                // Two finger pinch - handle zoom
                const touch1 = evt.nativeEvent.touches[0];
                const touch2 = evt.nativeEvent.touches[1];
                const distance = Math.sqrt(
                    Math.pow(touch2.pageX - touch1.pageX, 2) +
                    Math.pow(touch2.pageY - touch1.pageY, 2)
                );

                const deltaDistance = distance - lastDistanceRef.current;
                const zoomChange = deltaDistance * zoomSpeed;

                setZoom(prev => {
                    // Zoom out limit at 0.63, zoom in to 1.0
                    const newZoom = Math.max(0.63, Math.min(1.0, prev + zoomChange));
                    console.log('🔍 Female: Zoom applied:', zoomChange.toFixed(3), 'Total zoom:', newZoom.toFixed(2));
                    return newZoom;
                });

                lastDistanceRef.current = distance;
            } else {
                // Single finger drag - handle rotation
                const deltaX = evt.nativeEvent.pageX - lastTouchRef.current.x;

                // HORIZONTAL ONLY rotation (Y-axis) with 180-degree limit
                setRotationY(prev => {
                    const newRotation = prev + deltaX * rotationSpeed;
                    // Limit rotation to 180 degrees (π radians) from initial position
                    const initialRotation = 6.11;
                    const minRotation = initialRotation - Math.PI; // -180 degrees from initial
                    const maxRotation = initialRotation + Math.PI; // +180 degrees from initial
                    return Math.max(minRotation, Math.min(maxRotation, newRotation));
                });
                setRotationX(0); // Keep X rotation at 0 (no vertical tilt)

                lastTouchRef.current = {
                    x: evt.nativeEvent.pageX,
                    y: evt.nativeEvent.pageY,
                };

                console.log('🔄 Female: Horizontal rotation applied:', (deltaX * rotationSpeed).toFixed(3), 'Total Y rotation:', rotationY.toFixed(2));
            }
        },
        onPanResponderRelease: () => {
            setIsManualRotation(true);
        },
    });

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsModelLoaded(true);
            setTimeout(() => {
                setIsModelReady(true);
            }, 500);
        }, 1500);
        return () => clearTimeout(timer);
    }, []);

    const cameraSettings = getCameraSettings();

    return (
        <View style={styles.container}>
            {!isModelReady && (
                <View style={styles.loaderOverlay}>
                    <SvgLoader />
                </View>
            )}

            <View style={[styles.canvas, !isModelLoaded && styles.hiddenCanvas]} {...panResponder.panHandlers}>
                <ErrorBoundary>
                    <Canvas
                        style={styles.canvasInner}
                        camera={{ position: cameraSettings.position, fov: cameraSettings.fov }}
                        gl={{
                            antialias: true,
                            alpha: true,
                            powerPreference: "high-performance",
                        }}
                        frameloop="always"
                    >
                        {/* Light background for all models */}
                        <color attach="background" args={['#f8fafc']} />

                        {/* Soft lighting */}
                        <>
                            <ambientLight intensity={0.7} />
                            <directionalLight position={[8, 8, 4]} intensity={0.9} color="#ffffff" />
                            <directionalLight position={[-8, 4, -4]} intensity={0.6} color="#e8f4f8" />
                            <pointLight position={[0, 2, 0]} intensity={0.3} color="#fef7cd" />
                            <pointLight position={[0, -1, 1]} intensity={0.2} color="#ffffff" />
                            <spotLight
                                position={[0, 5, 10]}
                                angle={0.5}
                                intensity={2.0}
                                penumbra={1}
                                castShadow
                                decay={1}
                            />
                        </>

                        <Suspense fallback={null}>
                            <FemaleModel3D
                                rotationX={rotationX}
                                rotationY={rotationY}
                                isManualRotation={isManualRotation}
                                colorCombination={currentCombination}
                                userColors={userColors}
                                selectedCategory={selectedCategory}
                                zoom={zoom}
                            />
                        </Suspense>
                    </Canvas>
                </ErrorBoundary>
            </View>

            {/* Color Combinations Section */}
            {isLoadingRecommendations ? (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#e91e63" />
                    <Text style={styles.loadingText}>Loading current color recommendations...</Text>
                </View>
            ) : currentCombinations.length > 0 ? (
                <View style={styles.colorCombinationContainer}>
                    <Text style={styles.colorCombinationTitle}>
                        🤖 Current Gemini AI Recommendations ({currentCombinations.length} available)
                    </Text>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={styles.combinationScroll}
                        contentContainerStyle={styles.combinationScrollContent}
                    >
                        {currentCombinations.map((combination: ColorCombination, index: number) => (
                            <TouchableOpacity
                                key={index}
                                style={[
                                    styles.combinationCard,
                                    selectedCombination === index && styles.selectedCombination
                                ]}
                                onPress={() => setSelectedCombination(index)}
                            >
                                <Text style={styles.combinationNumber}>#{index + 1}</Text>
                                <View style={styles.colorPreview}>
                                    <View style={[styles.colorBox, { backgroundColor: combination.shirt }]} />
                                    <View style={[styles.colorBox, { backgroundColor: combination.pants }]} />
                                    <View style={[styles.colorBox, { backgroundColor: combination.shoes }]} />
                                </View>
                                <Text style={styles.combinationName}>{combination.name || 'Style'}</Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </View>
            ) : (
                <View style={styles.noColorsContainer}>
                    <Ionicons
                        name={colorSource === 'gemini' ? "sparkles" : colorSource === 'loading' ? "hourglass-outline" : "color-palette-outline"}
                        size={48}
                        color={colorSource === 'gemini' ? "#e91e63" : "#9ca3af"}
                    />
                    <Text style={[
                        styles.noColorsTitle,
                        colorSource === 'gemini' && { color: '#e91e63', fontWeight: '600' }
                    ]}>
                        {colorSourceMessage}
                    </Text>
                    {colorSource === 'none' && (
                        <Text style={styles.noColorsSubtitle}>
                            💡 Upload a photo for personalized color recommendations
                        </Text>
                    )}
                    {colorSource === 'gemini' && (
                        <Text style={styles.noColorsSubtitle}>
                            Based on your current face analysis
                        </Text>
                    )}
                </View>
            )}
        </View>
    );
}

export default function FemaleModelScreen() {
    const { user } = useAuth();
    const [selectedCategory, setSelectedCategory] = useState<ModelCategory | null>(null);

    if (user?.gender !== 'female') {
        return (
            <View style={styles.accessDeniedContainer}>
                <Ionicons name="lock-closed" size={60} color="#e74c3c" />
                <Text style={styles.accessDeniedTitle}>Access Restricted</Text>
                <Text style={styles.accessDeniedText}>
                    This 3D model is only available for female users.
                </Text>
                <Text style={styles.accessDeniedSubtext}>
                    Your profile shows: {user?.gender || 'Unknown gender'}
                </Text>
            </View>
        );
    }

    const handleCategorySelect = (category: ModelCategory) => {
        if ((category.id === 'cheongsam' || category.id === 'Women Formal Dress' || category.id === 'casual') && category.available) {
            setSelectedCategory(category);
        }
    };

    const handleBackToSelector = () => {
        setSelectedCategory(null);
    };

    if (!selectedCategory) {
        return <FemaleModelSelector onCategorySelect={handleCategorySelect} />;
    }

    return (
        <SafeAreaView style={styles.fullScreenContainer}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={handleBackToSelector}
                >
                    <Ionicons name="arrow-back" size={20} color="#e91e63" />
                    <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>

                <Text style={styles.categoryTitle}>{selectedCategory.name}</Text>
            </View>

            <View style={styles.modelViewerContainer}>
                <FemaleModel3DViewer selectedCategory={selectedCategory} />
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    fullScreenContainer: {
        flex: 1,
        backgroundColor: '#f1f5f9',
    },
    container: {
        flex: 1,
        position: 'relative',
        backgroundColor: '#f1f5f9',
    },
    canvas: {
        flex: 1,
    },
    hiddenCanvas: {
        opacity: 0,
    },
    canvasInner: {
        flex: 1,
    },
    loaderOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: '#f1f5f9',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    svgContainer: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    svgWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        marginTop: 20,
        fontSize: 16,
        color: '#e91e63',
        fontWeight: '600',
        textAlign: 'center',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f1f5f9',
    },
    errorText: {
        fontSize: 18,
        color: '#d32f2f',
        textAlign: 'center',
        fontWeight: '600',
    },
    accessDeniedContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f9fa',
        padding: 40,
    },
    accessDeniedTitle: {
        fontSize: 24,
        fontWeight: '700',
        color: '#e74c3c',
        marginTop: 20,
        marginBottom: 16,
        textAlign: 'center',
    },
    accessDeniedText: {
        fontSize: 16,
        color: '#2c3e50',
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 12,
    },
    accessDeniedSubtext: {
        fontSize: 14,
        color: '#7f8c8d',
        textAlign: 'center',
        fontStyle: 'italic',
    },
    backButtonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#ffffff',
        borderBottomWidth: 1,
        borderBottomColor: '#f1f5f9',
        paddingHorizontal: width < 375 ? 16 : 20,
        paddingVertical: height < 667 ? 12 : 16,
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        backgroundColor: '#f8fafc',
    },
    backButtonText: {
        fontSize: 16,
        fontWeight: '500',
        color: '#e91e63',
        marginLeft: 6,
    },
    categoryTitle: {
        fontSize: width < 375 ? 16 : 18,
        fontWeight: '600',
        color: '#1f2937',
    },
    modelViewerContainer: {
        flex: 1,
        minHeight: height * 0.6,
    },
    colorCombinationContainer: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#f1f5f9',
        paddingHorizontal: width < 375 ? 16 : 20,
        paddingVertical: height < 667 ? 12 : 16,
    },
    colorCombinationTitle: {
        fontSize: width < 375 ? 14 : 16,
        fontWeight: '600',
        color: '#1f2937',
        marginBottom: 12,
        textAlign: 'center',
    },
    combinationScroll: {
        flexGrow: 0,
    },
    combinationScrollContent: {
        paddingHorizontal: 4,
    },
    combinationCard: {
        backgroundColor: '#f8fafc',
        borderRadius: 12,
        padding: 12,
        marginHorizontal: 6,
        width: width < 375 ? 90 : 100,
        alignItems: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
    },
    selectedCombination: {
        borderColor: '#e91e63',
        backgroundColor: '#fdf2f8',
    },
    combinationNumber: {
        fontSize: 12,
        fontWeight: '600',
        color: '#e91e63',
        marginBottom: 8,
    },
    colorPreview: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 8,
        gap: 3,
    },
    colorBox: {
        width: width < 375 ? 16 : 20,
        height: width < 375 ? 16 : 20,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#e5e7eb',
    },
    combinationName: {
        fontSize: 10,
        color: '#6b7280',
        textAlign: 'center',
        lineHeight: 12,
    },
    noColorsContainer: {
        padding: width < 375 ? 24 : 32,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#ffffff',
        marginHorizontal: 16,
        marginBottom: 16,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
    },
    noColorsTitle: {
        fontSize: width < 375 ? 16 : 18,
        fontWeight: '600',
        color: '#374151',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    noColorsSubtitle: {
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        marginTop: 8,
        paddingHorizontal: 20,
    },
    loadingContainer: {
        padding: width < 375 ? 24 : 32,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#ffffff',
        marginHorizontal: 16,
        marginBottom: 16,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#e5e7eb',
    },
});