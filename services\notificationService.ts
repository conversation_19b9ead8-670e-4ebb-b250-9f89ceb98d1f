// Custom notification service for React Native
let toastContext: any = null;

export const setToastContext = (context: any) => {
  toastContext = context;
};

// Funny and friendly messages for different scenarios
export const NotificationMessages = {
  // Profile Update Messages
  profileUpdate: {
    success: [
      "🎉 Ho gaya tera profile update! Looking fresh! ✨",
      "🔥 Profile updated successfully! Tu toh star ban gaya! ⭐",
      "✅ Boom! Profile update complete! Tujhe dekh kar log jealous honge! 😎",
      "🎊 Profile updated! Ab tu bilkul perfect lag raha hai! 💯"
    ],
    error: [
      "😅 Oops! Profile update mein kuch gadbad ho gayi. Try kar phir se bhai! 🔄",
      "🤦‍♂️ Arre yaar, profile update nahi hua. Internet check kar le! 📶",
      "😬 Profile update fail ho gaya. Tension mat le, dobara try kar! 💪"
    ]
  },

  // Color Recommendation Messages
  colorRecommendation: {
    success: [
      "🌈 Wah! Tumhare best colors aa gaye! Ab tu fashion icon banega! 👑",
      "🎨 Color recommendations ready! Ye colors tere upar bomb lagenge! 💣",
      "✨ Best colors mil gaye! Ab tu rainbow se bhi zyada colorful! 🌈",
      "🔥 Color analysis complete! Ye colors tere personality ko match karte hain! 🎯"
    ],
    noFace: [
      "🤔 Bhai, face detect nahi ho raha! Camera ke saamne aa ja properly! 📸",
      "😄 Q kar raha hai bhai? Face dikhao camera mein! Sharma mat! 😊",
      "🙈 Arre face kahan hai? Hide and seek khel rahe ho kya? 😂",
      "📷 Face nahi dikh raha! Selfie mode on kar ke try kar! 🤳"
    ],
    processing: [
      "🔍 Analyzing your beautiful face... Wait kar thoda! ⏳",
      "🎨 Color magic chal rahi hai... Patience rakh bhai! ✨",
      "🧠 AI tumhare colors calculate kar raha hai... Almost done! 🤖"
    ],
    noRecommendations: [
      "🎨 Arre bhai, abhi tak color analysis nahi kiya! Pehle face scan kar! 📸",
      "🌈 Color recommendations nahi mili! Face analysis kar ke dekh! ✨",
      "🤳 Bhai, pehle selfie le kar face analysis kar! Phir colors milenge! 📱",
      "🎯 Color magic ke liye pehle face scan karna padega! Camera on kar! 📷"
    ]
  },

  // Internet/Network Messages
  network: {
    offline: [
      "📶 Arre yaar, internet bhi nahi hai! WiFi check kar le! 📡",
      "🌐 Network gayab hai bhai! Data on kar ya WiFi connect kar! 📱",
      "😅 Internet connection missing! Jio/Airtel ko call kar! 📞"
    ],
    slow: [
      "🐌 Internet slow chal raha hai! Patience rakh, loading ho raha! ⏳",
      "⏰ Network thoda slow hai! Coffee pi ke wait kar! ☕",
      "🚀 Slow internet detected! Rocket speed nahi mil raha! 🛸"
    ],
    error: [
      "💥 Server se connection nahi ho raha! Try again kar! 🔄",
      "🤖 Server mood off hai! Thoda wait kar ke try kar! 😴",
      "⚡ Network error! Internet provider ko gaali de ke try kar! 😂"
    ]
  },

  // General Success Messages
  success: {
    general: [
      "🎉 Success! Kaam ho gaya! 💯",
      "✅ Done! Tu champion hai! 🏆",
      "🔥 Perfect! Sab kuch smooth! ✨",
      "🎊 Completed! Tere jaisa koi nahi! 👑"
    ]
  },

  // General Error Messages
  error: {
    general: [
      "😅 Kuch toh gadbad hai! Try again kar bhai! 🔄",
      "🤦‍♂️ Error aa gaya! Tension mat le, fix kar denge! 🛠️",
      "😬 Something went wrong! Dobara attempt kar! 💪",
      "🙄 Error hui hai! Patience rakh, solve ho jayega! ⏳"
    ],
    validation: [
      "📝 Form mein kuch missing hai! Check kar le properly! ✏️",
      "⚠️ Details galat hain! Dhyan se fill kar! 🎯",
      "📋 Required fields bhool gaya! Complete kar pehle! ✅"
    ]
  },

  // Loading Messages
  loading: {
    general: [
      "⏳ Loading... Patience rakh bhai! 🙏",
      "🔄 Processing... Almost done! 💫",
      "⚡ Working on it... Wait kar thoda! ⏰"
    ]
  }
};

class NotificationService {
  private getRandomMessage(messages: string[]): string {
    return messages[Math.floor(Math.random() * messages.length)];
  }

  // Profile Update Notifications
  profileUpdateSuccess() {
    const message = this.getRandomMessage(NotificationMessages.profileUpdate.success);
    if (toastContext) {
      toastContext.success(message, 'Profile updated successfully!', {
        duration: 6000, // Increased for funny messages
      });
    }
  }

  profileUpdateError(error?: string) {
    const message = this.getRandomMessage(NotificationMessages.profileUpdate.error);
    const details = error ? `Error: ${error}` : 'Please try again';

    if (toastContext) {
      toastContext.error(message, details, {
        duration: 7000, // Increased for funny error messages
      });
    }
  }

  // Color Recommendation Notifications
  colorRecommendationSuccess() {
    const message = this.getRandomMessage(NotificationMessages.colorRecommendation.success);
    if (toastContext) {
      toastContext.success(message, 'Your personalized colors are ready!', {
        duration: 6000, // Increased for funny messages
      });
    }
  }

  noFaceDetected() {
    const message = this.getRandomMessage(NotificationMessages.colorRecommendation.noFace);
    if (toastContext) {
      toastContext.warning(message, 'Make sure your face is clearly visible', {
        duration: 7000, // Increased for funny warning messages
      });
    }
  }

  colorProcessing() {
    const message = this.getRandomMessage(NotificationMessages.colorRecommendation.processing);
    if (toastContext) {
      return toastContext.info(message, 'Analyzing your beautiful features...', {
        duration: 0, // Don't auto-dismiss
      });
    }
    return null;
  }

  noColorRecommendations() {
    const message = this.getRandomMessage(NotificationMessages.colorRecommendation.noRecommendations);
    if (toastContext) {
      toastContext.info(message, 'Go to Face Analysis to get your personalized colors!', {
        duration: 8000, // Longer duration for important info
      });
    }
  }

  // Network Error Notifications
  networkOffline() {
    const message = this.getRandomMessage(NotificationMessages.network.offline);
    if (toastContext) {
      toastContext.error(message, 'Check your internet connection', {
        duration: 8000, // Increased for network error messages
      });
    }
  }

  networkError(error?: string) {
    const message = this.getRandomMessage(NotificationMessages.network.error);
    const details = error ? `Details: ${error}` : 'Please try again';

    if (toastContext) {
      toastContext.error(message, details, {
        duration: 7000, // Increased for funny network error messages
      });
    }
  }

  // General Notifications
  success(customMessage?: string) {
    const message = customMessage || this.getRandomMessage(NotificationMessages.success.general);
    if (toastContext) {
      toastContext.success(message, '', {
        duration: 5000, // Increased for funny success messages
      });
    }
  }

  error(customMessage?: string, details?: string) {
    const message = customMessage || this.getRandomMessage(NotificationMessages.error.general);

    if (toastContext) {
      toastContext.error(message, details || 'Please try again', {
        duration: 6000, // Increased for funny error messages
      });
    }
  }

  loading(customMessage?: string) {
    const message = customMessage || this.getRandomMessage(NotificationMessages.loading.general);
    if (toastContext) {
      return toastContext.info(message, 'Please wait...', {
        duration: 0, // Don't auto-dismiss
      });
    }
    return null;
  }

  // Dismiss specific toast
  dismiss(toastId: any) {
    if (toastContext && toastId) {
      toastContext.dismissToast(toastId);
    }
  }

  // Dismiss all toasts
  dismissAll() {
    if (toastContext) {
      toastContext.dismissAll();
    }
  }
}

export const notificationService = new NotificationService();
