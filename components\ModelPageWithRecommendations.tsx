import { Canvas } from '@react-three/fiber';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useUserRecommendations } from '../hooks/useUserRecommendations';
import { ColorCombination } from '../services/colorService';
import { createMultipleColorCombinationsFromRecommendation } from '../utils/recommendationToColorCombination';
import { FormalDressModel } from './FormalDressModel';

interface ModelPageWithRecommendationsProps {
  // Optional: Allow override of color combination
  overrideColorCombination?: ColorCombination;
  // Optional: Show recommendation status
  showRecommendationStatus?: boolean;
}

export const ModelPageWithRecommendations: React.FC<ModelPageWithRecommendationsProps> = ({
  overrideColorCombination,
  showRecommendationStatus = true
}) => {
  const [selectedColorCombination, setSelectedColorCombination] = useState<ColorCombination | undefined>(overrideColorCombination);
  
  // User recommendation integration with navigation refresh
  const {
    hasRecommendations,
    mostRecentRecommendation,
    isLoading: recommendationsLoading,
    error: recommendationsError,
    refreshRecommendations,
    forceRefreshOnMount,
    triggerNavigationRefresh
  } = useUserRecommendations(true); // Enable auto-fetch (always fresh data)

  // Generate color combinations from user recommendations
  const [availableColorCombinations, setAvailableColorCombinations] = useState<ColorCombination[]>([]);

  // Rotation and tilt state
  const [modelRotation, setModelRotation] = useState(0);
  const [modelTilt, setModelTilt] = useState(0);

  useEffect(() => {
    if (hasRecommendations && mostRecentRecommendation) {
      // Create multiple color combinations from user recommendations
      const recommendedCombinations = createMultipleColorCombinationsFromRecommendation(mostRecentRecommendation);
      setAvailableColorCombinations(recommendedCombinations);
      
      // If no override is provided, use the first recommended combination
      if (!overrideColorCombination && recommendedCombinations.length > 0) {
        setSelectedColorCombination(recommendedCombinations[0]);
      }
    } else {
      // No recommendations available
      setAvailableColorCombinations([]);
      if (!overrideColorCombination) {
        setSelectedColorCombination(undefined);
      }
    }
  }, [hasRecommendations, mostRecentRecommendation, overrideColorCombination]);

  // Trigger fresh data fetch every time the page comes into focus
  const { useFocusEffect } = require('@react-navigation/native');
  useFocusEffect(
    React.useCallback(() => {
      console.log('🎨 ModelPageWithRecommendations: Page focused - clearing state and triggering navigation refresh');

      // Clear existing state to ensure fresh data
      setAvailableColorCombinations([]);
      setSelectedColorCombination(undefined);

      // Trigger fresh data fetch
      triggerNavigationRefresh();
    }, [triggerNavigationRefresh])
  );

  const renderRecommendationStatus = () => {
    if (!showRecommendationStatus) return null;

    if (recommendationsLoading) {
      return (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="small" color="#e91e63" />
          <Text style={styles.statusText}>Loading your color recommendations...</Text>
        </View>
      );
    }

    if (recommendationsError) {
      return (
        <View style={styles.statusContainer}>
          <Text style={styles.errorText}>Error loading recommendations</Text>
          <TouchableOpacity onPress={refreshRecommendations} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (hasRecommendations) {
      return (
        <View style={styles.statusContainer}>
          <Text style={styles.successText}>✨ Using your personalized color recommendations</Text>
          <Text style={styles.subText}>Based on your face analysis</Text>
        </View>
      );
    }

    return (
      <View style={styles.statusContainer}>
        <Text style={styles.infoText}>💡 Get personalized color recommendations</Text>
        <Text style={styles.subText}>Upload a photo for AI-powered color analysis</Text>
      </View>
    );
  };

  const renderColorOptions = () => {
    if (availableColorCombinations.length === 0) return null;

    return (
      <View style={styles.colorOptionsContainer}>
        <Text style={styles.colorOptionsTitle}>Your Recommended Styles:</Text>
        <View style={styles.colorOptionsGrid}>
          {availableColorCombinations.map((combination, index) => (
            <TouchableOpacity
              key={combination.id}
              style={[
                styles.colorOption,
                selectedColorCombination?.id === combination.id && styles.selectedColorOption
              ]}
              onPress={() => setSelectedColorCombination(combination)}
            >
              <View style={styles.colorPreview}>
                <View style={[styles.colorSwatch, { backgroundColor: combination.shirt }]} />
                <View style={[styles.colorSwatch, { backgroundColor: combination.shoes }]} />
              </View>
              <Text style={styles.colorOptionText}>{combination.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderRecommendationStatus()}
      
      <View style={styles.modelContainer}>
        <Canvas  camera={{ position: [0, 0, 1], fov: 20 }}>
          <ambientLight intensity={0.5} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <FormalDressModel
            dressColors={selectedColorCombination}
            modelScale={0.005}
            verticalOffset={-0.4}
            enableColorSync={true}
            displayMode="front"
            rotationY={modelRotation}
            tiltX={0.2}
          />
        </Canvas>
      </View>

      {/* Horizontal Rotation Controls (Limited Range) */}
      <View style={styles.rotationControls}>
        <TouchableOpacity
          style={styles.rotationButton}
          onPress={() => setModelRotation(Math.max(-Math.PI/2, modelRotation - Math.PI/6))}
        >
          <Text style={styles.rotationButtonText}>← Left</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.rotationButton}
          onPress={() => {
            setModelRotation(0);
            setModelTilt(0.4); // Reset to default tilt
          }}
        >
          <Text style={styles.rotationButtonText}>Center</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.rotationButton}
          onPress={() => setModelRotation(Math.min(Math.PI/2, modelRotation + Math.PI/6))}
        >
          <Text style={styles.rotationButtonText}>Right →</Text>
        </TouchableOpacity>
      </View>

      {/* Tilt Controls */}
      <View style={styles.rotationControls}>
        <TouchableOpacity
          style={styles.tiltButton}
          onPress={() => setModelTilt(Math.max(-Math.PI/6, modelTilt - Math.PI/12))}
        >
          <Text style={styles.rotationButtonText}>↑ Tilt Back</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tiltButton}
          onPress={() => setModelTilt(0)}
        >
          <Text style={styles.rotationButtonText}>Straight</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tiltButton}
          onPress={() => setModelTilt(Math.min(Math.PI/6, modelTilt + Math.PI/12))}
        >
          <Text style={styles.rotationButtonText}>↓ Tilt Front</Text>
        </TouchableOpacity>
      </View>

      {renderColorOptions()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  statusContainer: {
    padding: 16,
    backgroundColor: '#fff',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  statusText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  successText: {
    fontSize: 16,
    color: '#4caf50',
    fontWeight: '600',
  },
  errorText: {
    fontSize: 14,
    color: '#f44336',
  },
  infoText: {
    fontSize: 14,
    color: '#2196f3',
  },
  subText: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  retryButton: {
    marginTop: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#e91e63',
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  modelContainer: {
    height: 150, // Much smaller canvas height
    width: 200,  // Also limit the width
    backgroundColor: '#fff',
    alignSelf: 'center', // Center the small canvas
  },
  colorOptionsContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  colorOptionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  colorOptionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    backgroundColor: '#f9f9f9',
    minWidth: 100,
  },
  selectedColorOption: {
    borderColor: '#e91e63',
    backgroundColor: '#fce4ec',
  },
  colorPreview: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  colorSwatch: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  colorOptionText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  rotationControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  rotationButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#e91e63',
    borderRadius: 6,
    minWidth: 80,
  },
  rotationButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  tiltButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#2196f3', // Blue color for tilt buttons
    borderRadius: 6,
    minWidth: 80,
  },
});

export default ModelPageWithRecommendations;
