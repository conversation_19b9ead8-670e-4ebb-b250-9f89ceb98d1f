import { notificationService } from './notificationService';

export interface ErrorInfo {
  code: string;
  message: string;
  userMessage: string;
  solution: string;
  funnyMessage: string;
}

export const ErrorCodes = {
  // Network Errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  NO_INTERNET: 'NO_INTERNET',
  SERVER_ERROR: 'SERVER_ERROR',
  
  // Authentication Errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  
  // Validation Errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD: 'REQUIRED_FIELD',
  INVALID_EMAIL: 'INVALID_EMAIL',
  WEAK_PASSWORD: 'WEAK_PASSWORD',
  
  // Face Analysis Errors
  NO_FACE_DETECTED: 'NO_FACE_DETECTED',
  MULTIPLE_FACES: 'MULTIPLE_FACES',
  POOR_IMAGE_QUALITY: 'POOR_IMAGE_QUALITY',
  UNSUPPORTED_FORMAT: 'UNSUPPORTED_FORMAT',
  
  // Profile Errors
  PROFILE_UPDATE_FAILED: 'PROFILE_UPDATE_FAILED',
  PROFILE_FETCH_FAILED: 'PROFILE_FETCH_FAILED',
  
  // General Errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
} as const;

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes];

class ErrorHandlingService {
  private errorMap: Record<ErrorCode, ErrorInfo> = {
    // Network Errors
    [ErrorCodes.NETWORK_ERROR]: {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed',
      userMessage: 'Internet connection problem detected',
      solution: 'Check your internet connection and try again',
      funnyMessage: '📶 Arre yaar, internet bhi nahi hai! WiFi check kar le! 📡'
    },
    
    [ErrorCodes.TIMEOUT_ERROR]: {
      code: 'TIMEOUT_ERROR',
      message: 'Request timeout',
      userMessage: 'Request took too long to complete',
      solution: 'Server is slow. Please wait and try again',
      funnyMessage: '⏰ Server slow chal raha hai! Coffee pi ke wait kar! ☕'
    },
    
    [ErrorCodes.NO_INTERNET]: {
      code: 'NO_INTERNET',
      message: 'No internet connection',
      userMessage: 'You are offline',
      solution: 'Connect to WiFi or enable mobile data',
      funnyMessage: '🌐 Network gayab hai bhai! Data on kar ya WiFi connect kar! 📱'
    },
    
    [ErrorCodes.SERVER_ERROR]: {
      code: 'SERVER_ERROR',
      message: 'Server error occurred',
      userMessage: 'Server is having issues',
      solution: 'Our servers are busy. Please try again in a few minutes',
      funnyMessage: '🤖 Server mood off hai! Thoda wait kar ke try kar! 😴'
    },
    
    // Authentication Errors
    [ErrorCodes.INVALID_CREDENTIALS]: {
      code: 'INVALID_CREDENTIALS',
      message: 'Invalid email or password',
      userMessage: 'Login credentials are incorrect',
      solution: 'Check your email and password, or reset your password',
      funnyMessage: '🔐 Email ya password galat hai! Bhool gaya kya? Reset kar le! 🤔'
    },
    
    [ErrorCodes.TOKEN_EXPIRED]: {
      code: 'TOKEN_EXPIRED',
      message: 'Session expired',
      userMessage: 'Your session has expired',
      solution: 'Please login again to continue',
      funnyMessage: '⏰ Session expire ho gaya! Dobara login kar bhai! 🔄'
    },
    
    [ErrorCodes.UNAUTHORIZED]: {
      code: 'UNAUTHORIZED',
      message: 'Access denied',
      userMessage: 'You are not authorized for this action',
      solution: 'Login with proper credentials or contact support',
      funnyMessage: '🚫 Access denied! Tu authorized nahi hai bhai! 👮‍♂️'
    },
    
    // Validation Errors
    [ErrorCodes.VALIDATION_ERROR]: {
      code: 'VALIDATION_ERROR',
      message: 'Validation failed',
      userMessage: 'Some information is missing or incorrect',
      solution: 'Please check all fields and fill them correctly',
      funnyMessage: '📝 Form mein kuch missing hai! Check kar le properly! ✏️'
    },
    
    [ErrorCodes.REQUIRED_FIELD]: {
      code: 'REQUIRED_FIELD',
      message: 'Required field missing',
      userMessage: 'Please fill all required fields',
      solution: 'Complete all fields marked with * (asterisk)',
      funnyMessage: '⚠️ Required fields bhool gaya! Complete kar pehle! ✅'
    },
    
    [ErrorCodes.INVALID_EMAIL]: {
      code: 'INVALID_EMAIL',
      message: 'Invalid email format',
      userMessage: 'Email format is incorrect',
      solution: 'Enter a valid email address (<EMAIL>)',
      funnyMessage: '📧 Email format galat hai! @ aur .com toh daal de! 😅'
    },
    
    [ErrorCodes.WEAK_PASSWORD]: {
      code: 'WEAK_PASSWORD',
      message: 'Password too weak',
      userMessage: 'Password is not strong enough',
      solution: 'Use at least 8 characters with numbers and special characters',
      funnyMessage: '🔒 Password weak hai! Strong password banao, hacker se bachne ke liye! 💪'
    },
    
    // Face Analysis Errors
    [ErrorCodes.NO_FACE_DETECTED]: {
      code: 'NO_FACE_DETECTED',
      message: 'No face detected in image',
      userMessage: 'Could not detect a face in the image',
      solution: 'Make sure your face is clearly visible and well-lit',
      funnyMessage: '🤔 Bhai, face detect nahi ho raha! Camera ke saamne aa ja properly! 📸'
    },
    
    [ErrorCodes.MULTIPLE_FACES]: {
      code: 'MULTIPLE_FACES',
      message: 'Multiple faces detected',
      userMessage: 'Multiple faces found in the image',
      solution: 'Use an image with only one face for better analysis',
      funnyMessage: '👥 Kitne log hain photo mein! Sirf tera face chahiye! 📷'
    },
    
    [ErrorCodes.POOR_IMAGE_QUALITY]: {
      code: 'POOR_IMAGE_QUALITY',
      message: 'Image quality too low',
      userMessage: 'Image quality is not sufficient for analysis',
      solution: 'Use a clear, high-quality image with good lighting',
      funnyMessage: '📸 Photo quality kharab hai! HD mein photo le bhai! ✨'
    },
    
    [ErrorCodes.UNSUPPORTED_FORMAT]: {
      code: 'UNSUPPORTED_FORMAT',
      message: 'Unsupported file format',
      userMessage: 'File format is not supported',
      solution: 'Use JPG, PNG, or HEIC image formats only',
      funnyMessage: '🖼️ File format support nahi hai! JPG ya PNG use kar! 📁'
    },
    
    // Profile Errors
    [ErrorCodes.PROFILE_UPDATE_FAILED]: {
      code: 'PROFILE_UPDATE_FAILED',
      message: 'Profile update failed',
      userMessage: 'Could not update your profile',
      solution: 'Check your internet connection and try again',
      funnyMessage: '😅 Profile update mein kuch gadbad ho gayi. Try kar phir se bhai! 🔄'
    },
    
    [ErrorCodes.PROFILE_FETCH_FAILED]: {
      code: 'PROFILE_FETCH_FAILED',
      message: 'Failed to fetch profile',
      userMessage: 'Could not load your profile information',
      solution: 'Refresh the app or check your internet connection',
      funnyMessage: '📱 Profile load nahi ho raha! Refresh kar ke try kar! 🔄'
    },
    
    // General Errors
    [ErrorCodes.UNKNOWN_ERROR]: {
      code: 'UNKNOWN_ERROR',
      message: 'Unknown error occurred',
      userMessage: 'Something unexpected happened',
      solution: 'Please try again or contact support if the problem persists',
      funnyMessage: '🤷‍♂️ Kuch toh gadbad hai! Try again kar bhai! 🔄'
    },
    
    [ErrorCodes.PERMISSION_DENIED]: {
      code: 'PERMISSION_DENIED',
      message: 'Permission denied',
      userMessage: 'App needs permission to access this feature',
      solution: 'Grant the required permissions in your device settings',
      funnyMessage: '🔐 Permission nahi hai! Settings mein ja ke allow kar de! ⚙️'
    },
    
    [ErrorCodes.FILE_TOO_LARGE]: {
      code: 'FILE_TOO_LARGE',
      message: 'File size too large',
      userMessage: 'Selected file is too large',
      solution: 'Choose a smaller file (max 10MB) or compress the image',
      funnyMessage: '📁 File bahut badi hai! Chhoti file choose kar! 📏'
    },
  };

  // Handle error and show appropriate notification
  handleError(error: any, context?: string): ErrorInfo {
    console.error(`Error in ${context || 'unknown context'}:`, error);

    const errorInfo = this.getErrorInfo(error);

    // Show notification based on error type
    if (errorInfo.code === ErrorCodes.TOKEN_EXPIRED) {
      // Special handling for token expiration
      notificationService.error(
        '⏰ Session expire ho gaya! Dobara login kar bhai! 🔄',
        'Please login again to continue'
      );
    } else if (errorInfo.code === ErrorCodes.NO_FACE_DETECTED) {
      notificationService.noFaceDetected();
    } else if (errorInfo.code.includes('NETWORK') || errorInfo.code.includes('INTERNET')) {
      notificationService.networkError(errorInfo.funnyMessage);
    } else if (errorInfo.code.includes('PROFILE')) {
      notificationService.profileUpdateError(errorInfo.funnyMessage);
    } else {
      notificationService.error(errorInfo.funnyMessage, errorInfo.solution);
    }

    return errorInfo;
  }

  // Get error information from error object
  getErrorInfo(error: any): ErrorInfo {
    // Check if error has a specific code
    if (error?.code && this.errorMap[error.code as ErrorCode]) {
      return this.errorMap[error.code as ErrorCode];
    }

    // Check error message for common patterns
    const message = error?.message?.toLowerCase() || '';

    // Check for token expiration first
    if (error?.code === 'TOKEN_EXPIRED' ||
        message.includes('token expired') ||
        message.includes('session expired') ||
        (message.includes('unauthorized') && message.includes('token'))) {
      return this.errorMap[ErrorCodes.TOKEN_EXPIRED];
    }

    if (message.includes('network') || message.includes('fetch')) {
      return this.errorMap[ErrorCodes.NETWORK_ERROR];
    }

    if (message.includes('timeout')) {
      return this.errorMap[ErrorCodes.TIMEOUT_ERROR];
    }

    if (message.includes('unauthorized') || message.includes('401')) {
      return this.errorMap[ErrorCodes.UNAUTHORIZED];
    }

    if (message.includes('validation') || message.includes('required')) {
      return this.errorMap[ErrorCodes.VALIDATION_ERROR];
    }

    if (message.includes('face') && message.includes('not')) {
      return this.errorMap[ErrorCodes.NO_FACE_DETECTED];
    }

    if (message.includes('server') || message.includes('500')) {
      return this.errorMap[ErrorCodes.SERVER_ERROR];
    }

    // Default to unknown error
    return this.errorMap[ErrorCodes.UNKNOWN_ERROR];
  }

  // Show success notification
  showSuccess(type: 'profile' | 'color' | 'general', customMessage?: string) {
    switch (type) {
      case 'profile':
        notificationService.profileUpdateSuccess();
        break;
      case 'color':
        notificationService.colorRecommendationSuccess();
        break;
      case 'general':
        notificationService.success(customMessage);
        break;
    }
  }

  // Show loading notification
  showLoading(message?: string) {
    return notificationService.loading(message);
  }

  // Dismiss notifications
  dismissLoading(toastId: any) {
    notificationService.dismiss(toastId);
  }

  dismissAll() {
    notificationService.dismissAll();
  }
}

export const errorHandlingService = new ErrorHandlingService();
