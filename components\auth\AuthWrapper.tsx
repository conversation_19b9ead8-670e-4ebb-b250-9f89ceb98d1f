import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Dimensions, StyleSheet, Text, View } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { LoginScreen } from './LoginScreen';
import { RegisterScreen } from './RegisterScreen';

// Animated Loading Component
const AnimatedLoadingScreen: React.FC<{ showForceLogin: boolean; onForceLogin: () => void }> = ({ showForceLogin, onForceLogin }) => {
  const { width, height } = Dimensions.get('window');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const floatAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous animations
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      })
    );

    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    const floatAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(floatAnim, {
          toValue: -10,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(floatAnim, {
          toValue: 10,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );

    rotateAnimation.start();
    pulseAnimation.start();
    floatAnimation.start();

    return () => {
      rotateAnimation.stop();
      pulseAnimation.stop();
      floatAnimation.stop();
    };
  }, []);

  const rotateInterpolate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={[styles.modernLoadingContainer, { width, height }]}>
      {/* Background Pattern */}
      <View style={styles.backgroundPattern}>
        {[...Array(20)].map((_, i) => (
          <Animated.View
            key={i}
            style={[
              styles.backgroundDot,
              {
                left: (i % 5) * (width / 5),
                top: Math.floor(i / 5) * (height / 4),
                opacity: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 0.1],
                }),
              },
            ]}
          />
        ))}
      </View>

      {/* Main Loading Content */}
      <Animated.View
        style={[
          styles.loadingContent,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { translateY: floatAnim },
            ],
          },
        ]}
      >
        {/* Central Loading Icon */}
        <Animated.View
          style={[
            styles.centralIcon,
            {
              transform: [
                { rotate: rotateInterpolate },
                { scale: pulseAnim },
              ],
            },
          ]}
        >
          <Text style={styles.iconText}>🚀</Text>
        </Animated.View>

        {/* Loading Text */}
        <Animated.Text
          style={[
            styles.modernLoadingText,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          {showForceLogin ? 'Connection taking too long...' : 'Loading Face App...'}
        </Animated.Text>

        <Animated.Text
          style={[
            styles.modernLoadingSubtext,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          {showForceLogin ? 'Having trouble connecting to server' : 'Preparing your experience...'}
        </Animated.Text>

        {/* Loading Dots */}
        <View style={styles.loadingDots}>
          {[0, 1, 2].map((index) => (
            <Animated.View
              key={index}
              style={[
                styles.dot,
                {
                  opacity: fadeAnim,
                  transform: [
                    {
                      scale: pulseAnim.interpolate({
                        inputRange: [1, 1.1],
                        outputRange: [0.8, 1.2],
                      }),
                    },
                  ],
                },
              ]}
            />
          ))}
        </View>

        {/* Force Login Button */}
        {showForceLogin && (
          <Animated.View
            style={[
              styles.forceLoginContainer,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <Text style={styles.forceLoginButton} onPress={onForceLogin}>
              Continue to Login 🔑
            </Text>
          </Animated.View>
        )}
      </Animated.View>
    </View>
  );
};

export const AuthWrapper: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [showForceLogin, setShowForceLogin] = useState(false);
  const [tokenExpiredMessage, setTokenExpiredMessage] = useState<string | null>(null);
  const { isLoading, isAuthenticated, user, logout } = useAuth();

  console.log('AuthWrapper render - isLogin:', isLogin, 'isLoading:', isLoading, 'isAuthenticated:', isAuthenticated, 'user:', user?.name);

  // Check for token expiration message
  useEffect(() => {
    const checkTokenExpiration = async () => {
      try {
        const tokenExpired = await AsyncStorage.getItem('tokenExpired');
        if (tokenExpired === 'true') {
          setTokenExpiredMessage('⏰ Session expire ho gaya! Dobara login kar bhai! 🔄');
          // Clear the flag
          await AsyncStorage.removeItem('tokenExpired');

          // Auto-hide message after 10 seconds (increased for better readability)
          setTimeout(() => {
            setTokenExpiredMessage(null);
          }, 10000);
        }
      } catch (error) {
        console.log('Error checking token expiration:', error);
      }
    };

    if (!isAuthenticated && !isLoading) {
      checkTokenExpiration();
    }
  }, [isAuthenticated, isLoading]);

  // Add timeout to prevent infinite loading
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        console.log('AuthWrapper: ⏰ Loading timeout reached, showing force login option');
        setShowForceLogin(true);
      }, 15000); // 15 second timeout

      return () => clearTimeout(timeout);
    } else {
      setShowForceLogin(false);
    }
  }, [isLoading]);

  if (isLoading) {
    console.log('AuthWrapper: Showing modern loading screen');
    return (
      <AnimatedLoadingScreen
        showForceLogin={showForceLogin}
        onForceLogin={async () => {
          console.log('AuthWrapper: Force login pressed, clearing auth state');
          setShowForceLogin(false);
          // Force clear auth state to stop loading and show login
          try {
            await logout();
          } catch (error) {
            console.log('AuthWrapper: Error during force logout:', error);
          }
        }}
      />
    );
  }

  // If user becomes authenticated while in AuthWrapper, this component should not render
  // The parent component should handle the redirect
  if (isAuthenticated) {
    console.log('AuthWrapper: User is authenticated but AuthWrapper is still rendering - this should not happen');
  }

  return (
    <View style={styles.container}>
      {/* Token expiration message */}
      {tokenExpiredMessage && (
        <View style={styles.tokenExpiredBanner}>
          <Text style={styles.tokenExpiredText}>{tokenExpiredMessage}</Text>
          <Text style={styles.tokenExpiredSubtext}>Please login again to continue</Text>
        </View>
      )}

      {isLogin ? (
        <LoginScreen onSwitchToRegister={() => {
          console.log('AuthWrapper: Switching to register screen');
          setIsLogin(false);
        }} />
      ) : (
        <RegisterScreen onSwitchToLogin={() => {
          console.log('AuthWrapper: Switching to login screen');
          setIsLogin(true);
        }} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Modern Loading Styles
  modernLoadingContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#667eea',
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  centralIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
  },
  iconText: {
    fontSize: 40,
  },
  modernLoadingText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  modernLoadingSubtext: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 30,
  },
  loadingDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#667eea',
    marginHorizontal: 6,
  },
  forceLoginContainer: {
    marginTop: 20,
  },
  // Legacy styles (keeping for compatibility)
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    marginTop: 20,
    fontWeight: '600',
  },
  loadingSubtext: {
    color: '#e0e7ff',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  forceLoginButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 25,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  forceLoginText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  tokenExpiredBanner: {
    position: 'absolute',
    top: 50,
    left: 16,
    right: 16,
    backgroundColor: '#fef2f2',
    borderColor: '#fecaca',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tokenExpiredText: {
    color: '#dc2626',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  tokenExpiredSubtext: {
    color: '#7f1d1d',
    fontSize: 14,
    textAlign: 'center',
  },
});
