// Test Color Recommendations API Endpoint
// Run this script to test if the color recommendations endpoint exists

const BASE_URL = 'https://faceapp-ttwh.onrender.com/api';
const ANALYSIS_ID = '686776ecc9ef0b0e47ac4194'; // Your example analysis ID

// Test function
async function testColorRecommendationsAPI() {
  console.log('🧪 Testing Color Recommendations API...');
  console.log('=' .repeat(50));

  // List of possible endpoints to test
  const endpointsToTest = [
    `/face/analysis/${ANALYSIS_ID}/recommendations`,
    `/face/${ANALYSIS_ID}/recommendations`,
    `/analysis/${ANALYSIS_ID}/recommendations`,
    `/recommendations/${ANALYSIS_ID}`,
    `/face/analysis/${ANALYSIS_ID}/colors`,
    `/face/analysis/${ANALYSIS_ID}/color-recommendations`,
    `/color-recommendations/${ANALYSIS_ID}`,
  ];

  for (const endpoint of endpointsToTest) {
    await testEndpoint(endpoint);
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Testing completed!');
}

async function testEndpoint(endpoint) {
  const fullUrl = `${BASE_URL}${endpoint}`;
  
  try {
    console.log(`\n🔄 Testing: ${fullUrl}`);
    
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add authorization header if you have a token
        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
      },
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ SUCCESS!', {
        status: response.status,
        hasData: !!data,
        hasOutfits: !!data.data?.outfits,
        outfitCount: data.data?.outfits?.length || 0
      });
      
      if (data.data?.outfits) {
        console.log('👔 Found outfits:', data.data.outfits.map(o => o.outfitName));
      }
    } else {
      console.log('❌ FAILED:', {
        status: response.status,
        message: data.message || 'Unknown error'
      });
    }
  } catch (error) {
    console.log('❌ ERROR:', error.message);
  }
}

// Test specific analysis ID endpoint
async function testSpecificAnalysisId(analysisId) {
  console.log(`\n🎯 Testing specific analysis ID: ${analysisId}`);
  
  const endpoint = `/face/analysis/${analysisId}/recommendations`;
  await testEndpoint(endpoint);
}

// Test if the analysis ID exists
async function checkAnalysisExists(analysisId) {
  console.log(`\n🔍 Checking if analysis exists: ${analysisId}`);
  
  const possibleEndpoints = [
    `/face/analysis/${analysisId}`,
    `/face/${analysisId}`,
    `/analysis/${analysisId}`,
  ];

  for (const endpoint of possibleEndpoints) {
    await testEndpoint(endpoint);
  }
}

// Run tests
async function runAllTests() {
  await testColorRecommendationsAPI();
  await checkAnalysisExists(ANALYSIS_ID);
  await testSpecificAnalysisId(ANALYSIS_ID);
}

// Export for use in React Native or run directly
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { 
    testColorRecommendationsAPI, 
    testSpecificAnalysisId, 
    checkAnalysisExists,
    runAllTests 
  };
} else {
  // Run tests if in browser
  runAllTests();
}

// Instructions for use:
console.log(`
📋 INSTRUCTIONS:

1. In Browser Console:
   - Copy and paste this entire script
   - It will run automatically

2. In Node.js:
   - Save as test-color-recommendations.js
   - Run: node test-color-recommendations.js

3. Test with your own analysis ID:
   - Replace ANALYSIS_ID with your actual analysis ID
   - Run the script again

4. If you find a working endpoint:
   - Update the API service in your React Native app
   - Use the working endpoint format

🔧 TROUBLESHOOTING:

If all endpoints return 404:
- The color recommendations feature might not be implemented yet
- Check your backend API documentation
- Verify the analysis ID is valid
- Ensure you have proper authentication

If you get 401/403 errors:
- Add your authentication token to the headers
- Check if the endpoint requires specific permissions
`);
